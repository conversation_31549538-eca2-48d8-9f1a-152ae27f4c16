import {
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  View,
  TouchableOpacity,
  Image,
  SafeAreaView,
  useWindowDimensions,
  ActivityIndicator,
  Alert,
  Modal,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard
} from 'react-native';
import React, { useEffect, useState, useRef } from 'react';

import SelectDropdown from 'react-native-select-dropdown';
import DatePicker from 'react-native-modern-datepicker';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import Icon from 'react-native-vector-icons/FontAwesome';
import Icon1 from 'react-native-vector-icons/Ionicons';
import { launchImageLibrary } from 'react-native-image-picker';
import axios from 'axios';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { API } from '@env';
import { NEXT_GOOGLE_MAPS_KEY } from '@env';
import { useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Picker } from '@react-native-picker/picker';
import { getLoginToken } from '../../helpers';
import { useAuth } from '../../components/Auth/AuthContext';
import CustomMultiSelect from '../../components/CustomMultiSelect';
import Svg, { Path } from 'react-native-svg';
import { MultipleSelectList } from 'react-native-dropdown-select-list';
//   import TermsAndConditionsModal from '../../components/TermsAndConditionsModal ';
import moment from 'moment-timezone';

import { Country, State, City } from 'country-state-city';
import FontAwesome from'react-native-vector-icons/FontAwesome';


import QuillEditor, { QuillToolbar } from 'react-native-cn-quill';
import { RadioButton } from 'react-native-paper';
import CustomRadioButton from '../../components/CustomRadioButton/CustomRadioButton';
import TermsAndConditionsModal from '../../components/TermsAndConditionModal';

const accountTypeOptions = ["Working Individually", "Joining via Academy"]
// Remove static academies, use dynamic fetch

// Insert this helper function near the top of the file, after imports or before the OneStepForm component
function buildPayload(values) {
  console.log("values in buildPayload : ", values);
  const affiliationType = values.accountType === "Joining via Academy" ? "academy" : "individual";

  // Academy info
  // let academyName = "";
  let academyId = "";
  if (affiliationType === "academy") {
    // academyName = values.academy.name || "";
    academyId = values.academyId;
  }

  // Only build academyAvailability if needed
  let academyAvailability;
  if (affiliationType === "academy") {
    let startDate = values.academyStartDate ? values.academyStartDate.replace(/\//g, "-") : "";
    let endDate = "";
    if (values.academyEndDate) {
      endDate = values.academyEndDate.replace(/\//g, "-");
    } else if (startDate) {
      // Add 10 years to the startDate
      const [year, month, day] = startDate.split("-");
      endDate = `${parseInt(year, 10) + 10}-${month}-${day}`;
    }
    academyAvailability = {
      startDate,
      endDate,
      days: (values.selectedDays || []).map(day => typeof day === 'string' ? day : day.value),
      startTime: values.academyStartTime || "",
      endTime: values.academyEndTime || "",
    };
  }

  // Base payload
  const payload = {
    firstName: values.firstName,
    lastName: values.lastName,
    gender: values.gender,
    email: values.email,
    mobile: Number(values.mobile),
    dob: values.dob ? values.dob.split("T")[0] : "",
    password: values.password,
    confirmPassword: values.confirmPassword,
    profileImg: values.profileImg,
    age: Number(values.age),
    alternateMobile: values.alternateMobile,
    linkedFacilities: (values.linkedFacilities || []).map(fac => ({
      name: fac.name,
      addressLine1: fac.addressLine1,
      addressLine2: fac.addressLine2,
      city: fac.city,
      state: fac.state,
      pinCode: fac.pinCode,
      country: fac.country,
      amenities: fac.amenities,
    })),
    experience: Number(values.experience),
    language: values.language,
    sportsCategories: values.sportsCategories,
    privacyPolicyAccepted: values.privacyPolicyAccepted,
    coachingQualifications: values.coachingQualifications || [],
    coachingExperience: values.coachingExperience || [],
    playerExperience: values.playerExperience || [],
    award: values.award || [],
    kycDocuments: {
      documentName: values.kycDocuments.documentName || "",
      documentNumber: values.kycDocuments.documentNumber,
      documentImg: (values.kycDocuments.documentImg || []).map(img => ({ url: img.url })),
    },
    aadhaarNumber: values.aadhaarNumber,
    aadhaarImage: values.aadhaarImage || [],
    bankDetails: {
      accountNumber: Number(values.bankDetails.accountNumber),
      accountHolderName: values.bankDetails.accountHolderName,
      ifsc: values.bankDetails.ifsc,
    },
    hasGst: values.hasGst,
    gstNumber: values.gstNumber,
    gstState: values.gstState,
    affiliationType,
  };

  // Only add academy fields if affiliationType is 'academy'
  if (affiliationType === "academy") {
    payload.academyId = academyId;
    payload.coachShare = Number(values.coachShare);
    payload.academyShare = Number(values.academyShare);
    payload.academyAvailability = academyAvailability;
  }

  return payload;
}

const OneStepForm = ({ route }) => {
  const [open, setOpen] = useState(false);
  const [profileImage, setProfileImage] = useState('');
  const [openAcademyStartDate, setOpenAcademyStartDate] = useState(false);
  const [openAcademyEndDate, setOpenAcademyEndDate] = useState(false);
  const [openAcademyStartTime, setOpenAcademyStartTime] = useState(false);
  const [openAcademyEndTime, setOpenAcademyEndTime] = useState(false);
  const [loginToken, setLoginToken] = useState('');
  const navigation = useNavigation();
  const { user, updateUserDetails, setIsHeader, setPageName } = useAuth();

  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Unified image upload states
  const [imageUploadStates, setImageUploadStates] = useState({
    profile: false,
    kycFront: false,
    kycBack: false,
    aadharFront: false,
    aadharBack: false,
    coachingQualifications: {},  // index-based loading
    coachingExperience: {},
    playerExperience: {},
    award: {}
  });

  // Helper functions for upload state management
  const setUploadState = (fieldName, index, isUploading) => {
    setImageUploadStates(prev => {
      if (fieldName === 'profile' || fieldName === 'kycFront' || fieldName === 'kycBack' || fieldName === 'aadharFront' || fieldName === 'aadharBack') {
        return { ...prev, [fieldName]: isUploading };
      } else {
        return {
          ...prev,
          [fieldName]: { ...prev[fieldName], [index]: isUploading }
        };
      }
    });
  };

  const isAnyImageUploading = () => {
    const { profile, kycFront, kycBack, aadharFront, aadharBack, coachingQualifications, coachingExperience, playerExperience, award } = imageUploadStates;

    // Check simple boolean states
    if (profile || kycFront || kycBack || aadharFront || aadharBack) return true;

    // Check index-based states
    const indexBasedStates = [coachingQualifications, coachingExperience, playerExperience, award];
    return indexBasedStates.some(stateObj => Object.values(stateObj).some(state => state));
  };
  //console.log("user in registration ",user)
//professional details


  //!--professionaL
    const [coachId, setCoachId] = useState();
    const [categories, setCategories] = useState([]);
    const [awardImages, setAwardImages] = useState([]);
    const [playingExperienceImages, setPlayingExperienceImages] = useState([]);
    const [coachingExperienceImages, setCoachingExperienceImages] = useState([]);
    const [coachingQualificationsImages, setCoachingQualificationsImages] =
      useState([]);

    const [selectedItems, setSelectedItems] = useState([]);
    const [selectedDays, setSelectedDays] = useState([]);
    const [alertModal, setAlertModal] = useState(true);
    const [modalVisible, setModalVisible] = useState(false);
    const [saveData, setSaveData] = useState(null)
  const [isUserAuthorized, setIsUserAuthorized] = useState(false);
  const [filteredAcademies, setFilteredAcademies] = useState([]);
  const [academySearchLoading, setAcademySearchLoading] = useState(false);

  // Debounce ref for academy search
  const academySearchTimeout = useRef();

  // Fetch academies from API as user types
  const handleSearchInput = (searchText) => {
    if (academySearchTimeout.current) {
      clearTimeout(academySearchTimeout.current);
    }
    if (!searchText || searchText.trim() === '') {
      setFilteredAcademies([]);
      setAcademySearchLoading(false);
      return;
    }
    setAcademySearchLoading(true);
    academySearchTimeout.current = setTimeout(async () => {
      try {
        const response = await axios.get(`${API}/api/academy/?name=${encodeURIComponent(searchText)}`);
        const academies = response?.data?.data?.academies || [];
        setFilteredAcademies(academies);
      } catch (error) {
        setFilteredAcademies([]);
      }
      setAcademySearchLoading(false);
    }, 400);
  };

  // Refs for auto-scroll functionality
  const scrollViewRef = useRef(null);
  const fieldRefs = useRef({
    profileImg: null,
    firstName: null,
    lastName: null,
    mobile: null,
    alternateMobile: null,
    email: null,
    password: null,
    confirmPassword: null,
    dob: null,
    gender: null,
    linkedFacilities: null,
    experience: null,
    language: null,
    sportsCategories: null,
    documentNumber: null,
    documentImg: null,
    aadhaarNumber: null,
    aadhaarImage: null,
    accountHolderName: null,
    accountNumber: null,
    ifsc: null,
    gstNumber: null,
    academyRef: null,
    accountType: null,
  });

  //!-> kyc details
//  const route = useRoute();
  const { showMessage } = route.params || { showMessage: false };
  const [documentImageFront, setDocumentImageFront] = useState('');
  const [documentImageBack, setDocumentImageBack] = useState('');
  const [aadharImageFront, setAadharImageFront] = useState('');
  const [aadharImageBack, setAadharImageBack] = useState('');
  const [isEditable, setIsEditable] = useState(false);
  const [coachDetails, setCoachDetails] = useState({});

  const [states, setStates] = useState([]);
  const [isStateDropDown, setIsStateDropDown] = useState(false);
  const [selectedState, setSelectedState] = useState('')
  // const route = useRoute();
  const url = 'https://ft44cpdhke.execute-api.ap-south-1.amazonaws.com/';

  let coachData;
  let token;
  let id;
  useEffect(() => {
    const stateData = State.getStatesOfCountry("IN");
    setStates(stateData);
  }, []);
  useEffect(() => {
    const fetchData = async () => {
      try {
        const { source } = route.params || { source: 'direct' };
        if (user.length === 0) {
          coachData = await getLoginToken();
          setLoginToken(coachData?.data?.token);
          setCoachId(coachData?.data?.id);
          setIsHeader(true);
          if (coachData?.data?.experience && coachData?.data?.experience == '') {
            setIsUserAuthorized(false);
          }
          if (
            coachData?.data?.experience &&
            coachData?.data?.experience !== undefined
          ) {
            if (coachData.data.authStatus === 'unauthorized') {
              setIsUserAuthorized(true);
            }
          }
        } else {
          token = await user?.data?.token;
          setLoginToken(token);
          id = await user?.data?._id;
          setCoachId(id);
          if (user?.data?.experience && user?.data?.experience == '') {
            setIsUserAuthorized(false);
          } else if (user?.data?.experience && user?.data?.experience !== '') {
            if (user?.data?.authStatus === 'unauthorized') {
              setIsUserAuthorized(true);
            }
          }
          await getCoachDetails(user);
        }
        await getCategories();
      } catch (error) {
        // Optionally handle error
      } finally {
        setLoading(false);
      }
    };
    // fetchData();
  }, [user, coachId, loginToken]);
  useEffect(()=>{
    getCategories();
  },[])
  const gstRegex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;
  const panRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;
  const aadharRegex = /^[0-9]{12}$/;
  const getCategories = async () => {
    try {
      let response = await axios.get(`${API}/api/category`);
      //console.log(response?.data, "oo get categoriessssss");
      setCategories(response?.data?.data);
    } catch (error) {
      console.log(error);
    }
  };
  const getCoachDetails = async coachData => {
    try {

      formik.setValues({ ...formik?.values, ...coachData?.data });

      formik.setFieldValue('experience', coachData?.data?.experience);

      let temp = [];
      setCoachDetails(coachData.data);
      coachData?.data?.sportsCategories.map(x => {
        // console.log(x);
        temp.push({ label: x, value: x });
      });
      setSelectedItems(temp);
      (token = coachData?.data?.token), setLoginToken(token);
      id = coachData?.data?._id;

      await formik.setValues({ ...formik.values, ...coachData?.data });
      const finalDate = await formatDateToYYYYMMDD(coachData?.data?.dob);
      await formik.setFieldValue('dob', finalDate);

      coachData?.data?.linkedFacilities?.map(async (item, index) => {
        // Removed: console.log('insidde setting values', item?.description, item);
        await formik.setFieldValue(
          `linkedFacilities.${index}.description`,
          item?.description,
        );
      });
      if (
        !coachData?.data?.kycDocuments.documentNumber ||
        coachData?.data?.kycDocuments.documentNumber === '' ||
        coachData?.data?.kycDocuments.documentImg.length == 0
      ) {
        //setIsEditable(true);
        setIsEditable(false);
      } else {
        setIsEditable(true);
        //setIsEditable(false);
      }

      formik.setValues({ ...formik?.values, ...coachData?.data });

      if (coachData?.data?.kycDocuments?.documentImg?.length > 1) {
        setDocumentImageFront(
          coachData?.data?.kycDocuments?.documentImg[0]?.url,
        );
        setDocumentImageBack(
          coachData?.data?.kycDocuments?.documentImg[1]?.url,
        );
      } else if (coachData?.data?.kycDocuments?.documentImg?.length == 1) {
        setDocumentImageFront(
          coachData?.data?.kycDocuments?.documentImg[0]?.url,
        );
      }

      // Handle Aadhar images
      if (coachData?.data?.aadhaarImage?.length > 1) {
        setAadharImageFront(coachData?.data?.aadhaarImage[0]);
        setAadharImageBack(coachData?.data?.aadhaarImage[1]);
      } else if (coachData?.data?.aadhaarImage?.length == 1) {
        setAadharImageFront(coachData?.data?.aadhaarImage[0]);
      }

      setProfileImage(coachData?.data?.profileImg);
      let tempCoachingQualificationImages = [];
      let tempCoachingExperienceImages = [];
      let tempAwardImages = [];
      let tempPlayingExperienceImages = [];

      await Promise.all(
        coachData?.data?.coachingQualifications?.map(async x => {
          //console.log("first inside ")
          tempCoachingQualificationImages.push({
            image: x.image,
          });
        }),
      );
      setCoachingQualificationsImages([
        ...coachingQualificationsImages,
        ...tempCoachingQualificationImages,
      ]);
      //console.log("after setting images 11111", coachingQualificationsImages)

      //coaching experience
      await Promise.all(
        coachData?.data?.coachingExperience?.map(async x => {
          tempCoachingExperienceImages.push({
            image: x.image,
          });
        }),
      );
      await setCoachingExperienceImages([
        ...coachingExperienceImages,
        ...tempCoachingExperienceImages,
      ]);

      //awards images
      await Promise.all(
        coachData?.data?.award?.map(async x => {
          tempAwardImages.push({
            image: x.image,
          });
        }),
      );
      setAwardImages([...awardImages, ...tempAwardImages]);
      //playing experience
      await Promise.all(
        coachData?.data?.playerExperience?.map(async x => {
          tempPlayingExperienceImages.push({
            image: x.image,
          });
        }),
      );
      setPlayingExperienceImages([
        ...playingExperienceImages,
        ...tempPlayingExperienceImages,
      ]);

      //console.log("after setting images 11111", coachingQualificationsImages)
    } catch (error) {
      console.log(error); // KEEP: coach details fetch errors
    }
  };
  // Unified delete image function
  const deleteImage = async (fieldName, index = null, imageType = null, url) => {
    try {
      // Show confirmation dialog for profile image
      if (fieldName === 'profile') {
        await new Promise((resolve, reject) => {
          Alert.alert(
            'Profile Picture Delete',
            'Do you really want to delete, clicking OK will delete this profile picture.',
            [
              {
                text: 'Cancel',
                onPress: () => reject(new Error('User Cancelled')),
                style: 'cancel',
              },
              { text: 'Ok', onPress: resolve },
            ],
            { cancelable: true },
          );
        });
      }

      const formData = new FormData();
      formData.append('url', url);

      const response = await axios.post(
        `${API}/api/coach/uploadImage`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        },
      );

      // Handle different field types
      if (fieldName === 'profile') {
        setProfileImage('');
        formik.setFieldValue('profileImg', '');
      } else if (fieldName === 'kycDocuments') {
        if (imageType === 'front') {
          setDocumentImageFront('');
          const currentImages = formik.values.kycDocuments.documentImg;
          if (currentImages.length === 1) {
            formik.setFieldValue('kycDocuments.documentImg', []);
          } else if (currentImages.length === 2) {
            formik.setFieldValue('kycDocuments.documentImg', [currentImages[1]]);
          }
        } else if (imageType === 'back') {
          setDocumentImageBack('');
          const currentImages = formik.values.kycDocuments.documentImg;
          if (currentImages.length === 2) {
            formik.setFieldValue('kycDocuments.documentImg', [currentImages[0]]);
          }
        }
      } else if (fieldName === 'aadhaarImage') {
        if (imageType === 'front') {
          setAadharImageFront('');
          const currentImages = formik.values.aadhaarImage || [];
          if (currentImages.length === 1) {
            // Only front image exists, remove it completely
            formik.setFieldValue('aadhaarImage', []);
          } else if (currentImages.length === 2) {
            // Both images exist, remove front (index 0), keep back (index 1)
            formik.setFieldValue('aadhaarImage', [currentImages[1]]);
          }
          formik.setFieldTouched('aadhaarImage', true);
        } else if (imageType === 'back') {
          setAadharImageBack('');
          const currentImages = formik.values.aadhaarImage || [];
          if (currentImages.length === 2) {
            // Both images exist, remove back (index 1), keep front (index 0)
            formik.setFieldValue('aadhaarImage', [currentImages[0]]);
          } else if (currentImages.length === 1 && currentImages[0] === '') {
            // Only back image exists (front is empty), remove completely
            formik.setFieldValue('aadhaarImage', []);
          }
          formik.setFieldTouched('aadhaarImage', true);
        }
      } else {
        // Handle professional details images
        await formik.setFieldValue(`${fieldName}.${index}.image`, '');

        if (fieldName === 'coachingQualifications') {
          const updatedImages = [...coachingQualificationsImages];
          updatedImages[index] = {
            description: formik.values.coachingQualifications[index].description,
            image: '',
          };
          setCoachingQualificationsImages([...updatedImages]);
        } else if (fieldName === 'award') {
          const updatedImages = [...awardImages];
          updatedImages[index] = {
            description: formik.values.award[index].description,
            image: '',
          };
          setAwardImages([...updatedImages]);
        } else if (fieldName === 'playerExperience') {
          const updatedImages = [...playingExperienceImages];
          updatedImages[index] = {
            description: formik.values.playerExperience[index].description,
            image: '',
          };
          setPlayingExperienceImages([...updatedImages]);
        } else if (fieldName === 'coachingExperience') {
          const updatedImages = [...coachingExperienceImages];
          updatedImages[index] = {
            description: formik.values.coachingExperience[index].description,
            image: '',
          };
          setCoachingExperienceImages([...updatedImages]);
        }
      }
    } catch (error) {
      if (error.message !== 'User Cancelled') {
        console.log('error', error);
      }
    }
  };

  //--------professional details-----------------

  const { width } = useWindowDimensions();
  const _editor = React.createRef();

  const phoneRegExp = /^[6-9]\d{9}$/;
  const pinCodeRegExp = /^[1-9][0-9]{5}$/;
  const emailRegExp = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;
  const passwordRegExp = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^\w\s])[A-Za-z\d!"#$%&'()*+,-./:;<=>?@[\\\]^_`{|}~]{8,}$/;

  // let token;

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);

      const coachData = await getLoginToken();
      console.log("coach dataaaaa", coachData); // KEEP: login token fetch debug
      const token = coachData?.data?.token;
      setLoginToken(token);
      const id = coachData?.data?._id;
      //console.log("coach dataaaaa", token)
      getCoachDetails(user);
      setTimeout(() => {
        setLoading(false);
      }, 3000);
      //setLoading(false);
    };

    if (user.length === 0) {
      setLoading(false);
    } else {
      fetchData();
    }
  }, []);

  const getLocationInfo = async pincode => {
    try {
      const response = await axios.get(
        `https://api.postalpincode.in/pincode/${pincode}`,
      );
      if (
        response.data.length > 0 &&
        response.data[0]?.PostOffice?.length > 0
      ) {
        const postOffice = response.data[0].PostOffice[0];
        const locationInfo = {
          city: `${postOffice.Name},${postOffice.District}`,
          state: postOffice.State,
          country: postOffice.Country,
        };
        return locationInfo;
      } else {
        return null;
      }
    } catch (error) {
      console.error('Error fetching location:', error);
      return null;
    }
  };

  const handlePincodeChange = async (newPincode, index) => {
    formik.setFieldValue(`linkedFacilities.${index}.pinCode`, newPincode);
    if (newPincode?.length === 6) {
      const locationInfo = await getLocationInfo(newPincode);
      // Removed: console.log(locationInfo);
      const { city, country, state } = locationInfo;
      formik.setFieldValue(`linkedFacilities.${index}.city`, city);
      formik.setFieldValue(`linkedFacilities.${index}.country`, country);
      formik.setFieldValue(`linkedFacilities.${index}.state`, state);
    }
  };

  function formatDateToYYYYMMDD(dateString) {
    //console.log("datee in formatt",dateString)
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = `0${date.getMonth() + 1}`.slice(-2); // Add leading zero if needed
    const day = `0${date.getDate()}`.slice(-2); // Add leading zero if needed

    return `${day}-${month}-${year}`;
  }

  const getGeoLocations = async address => {
    const apiKey = NEXT_GOOGLE_MAPS_KEY;
    try {
      const url = `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(
        address,
      )}&key=${apiKey}`;
      const response = await axios.get(url);
      if (response.data.status === 'OK') {
        const result = response.data.results[0];
        const location = result.geometry.location;
        return {
          message: 'Success',
          data: { Latitude: location.lat, Longitude: location.lng },
        };
      } else {
        console.error(
          'Geocode was not successful for the following reason',
          response.data.status,
        );
        return null;
      }
    } catch (error) {
      console.error('There was an error fetching the geocode data:', error);
      return null;
    }
  };

  const validationSchema = Yup.object().shape({
    profileImg: Yup.string().required('Profile image is required'),
    firstName: Yup.string()
      .min(3, 'Atleast 3 characters are required')
      .required('Name is required'),
    lastName: Yup.string()
      .min(3, 'Atleast 3 characters are required')
      .required('Last name is required'),
    gender: Yup.string().required('Gender is required'),
    email: Yup.string()
      .email('Invalid email')
      .matches(emailRegExp, 'Invalid email')
      .required('Email is required'),
    dob: Yup.date()
      .required('Date of birth is required')
      .test('is-valid-date', 'Invalid date format', function (value) {
        if (!value) return false; // Required field, so empty values should fail with required message
        return !isNaN(new Date(value).getTime());
      })
      .test('not-future', 'Date of birth cannot be in the future', function (value) {
        if (!value) return false; // Required field, so empty values should fail with required message
        return new Date(value) <= new Date();
      })
      .test('is-adult', 'Must be at least 18 years old', function (value) {
        if (!value) return false; // Required field, so empty values should fail with required message
        const today = new Date();
        const minAgeDate = new Date(
          today.getFullYear() - 18,
          today.getMonth(),
          today.getDate(),
        );
        return new Date(value) <= minAgeDate;
      }),
    password: !loginToken
      ? Yup.string()
        .min(6, 'Password must be at least 6 characters')
        .matches(
          passwordRegExp,
          'Password must contain minimum eight characters, at least one uppercase letter, one lowercase letter, one number, and one special character.',
        )
        .required('Password is required')
      : Yup.string(),

    confirmPassword: !loginToken
      ? Yup.string()
        .required('Confirm Password is required')
        .oneOf([Yup.ref('password'), null], 'Password must match')
      : Yup.string(),

      mobile: Yup.string()
      .required('Please enter your phone number')
      .min(10, 'Phone number must be at least 10 digits')
      .max(10, 'Phone number must be at most 10 digits')
      .matches(phoneRegExp, 'Phone number must start with 6, 7, 8, or 9'),
    
    alternateMobile: Yup.string()
      .nullable()
      .notRequired()
      .test(
        'is-valid-phone',
        'Alternate phone number must be exactly 10 digits and start with 6-9',
        value => {
          if (!value) return true;
          return phoneRegExp.test(value);
        }
      )
      .test(
        'not-same-as-mobile',
        'Alternate mobile number must be different from primary mobile number',
        function (value) {
          const { mobile } = this.parent;
          if (!value || !mobile) return true; // Skip if either is empty
          return value !== mobile;
        }
      ),
     kycDocuments: Yup.object().shape({
            documentNumber: Yup.string()
              .required('Document Number is required')
              .matches(
                panRegex,
                'Must be a valid pan number with all upper case character',
              ),
            documentImg: Yup.array()
              .of(
                Yup.object().shape({
                  url: Yup.string().required('Document Images are required'),
                }),
              )
              .min(1, 'PAN card image is required'),
          }),
      aadhaarNumber: Yup.string()
        .matches(aadharRegex, "Must be a valid 12-digit Aadhar number")
        .required("Aadhar number is required"),
      aadhaarImage: Yup.array()
        .min(2, "Please upload both front and back images of Aadhar")
        .max(2, "Only 2 images are allowed")
        .of(Yup.string().required("Image URL is required")),
      bankDetails: Yup.object().shape({
          accountNumber: Yup.number().required('Account Number is required'),
              accountHolderName: Yup.string()
                    .max(30, 'Only 30 characters are allowed')
                    .required('Account Holder Name is required'),
              ifsc: Yup.string().required('IFSC Code is required'),
          }),
              hasGst: Yup.boolean(),
              gstState: Yup.string(),
              gstNumber: Yup.string()
                  .matches(gstRegex, "Please enter a valid GST number")
                  .when("hasGst", {
                    is: true,
                    then: (schema) => schema.required("GST Number is required"),
                        otherwise: (schema) => schema.notRequired(),
          }),
    // .required("Please enter your phone number"),
    linkedFacilities: Yup.array().of(
      Yup.object().shape({
        name: Yup.string()
          .required('Name is required')
          .max(100, 'Only 100 characters are allowed')
          .min(3, 'Minimum 3 characters are required'),
        addressLine1: Yup.string().required('Address Line 1 is required'),
        addressLine2: Yup.string(),
        city: Yup.string()
          .required('City is required')
          .max(50, 'Only 30 characters are allowed'),
        state: Yup.string()
          .required('State is required')
          .max(50, 'Only 30 characters are allowed'),
        pinCode: Yup.string()
          .matches(pinCodeRegExp, 'PIN code is not valid')
          .required('PinCode is required'),
        country: Yup.string()
          .required('Country is required')
          .max(50, 'Only 30 characters are allowed'),
        // phone: Yup.string().matches(phoneRegExp, "Phone number is not valid"),
        description: Yup.string(),
      }),

    ),

experience: Yup.mixed()
        .required('Experience is required')
        .test('is-valid-number', 'Only numbers are allowed', function (value) {
          // Check if value is empty, null, undefined, or NaN
          if (value === null || value === undefined || value === '' ||
              (typeof value === 'string' && value.trim() === '') ||
              (typeof value === 'number' && isNaN(value))) {
            return false; // Required field, so empty/null/NaN values should fail with required message
          }
          return !isNaN(Number(value));
        })
        .test('is-integer', 'Please enter an integer', function (value) {
          // Check if value is empty, null, undefined, or NaN
          if (value === null || value === undefined || value === '' ||
              (typeof value === 'string' && value.trim() === '') ||
              (typeof value === 'number' && isNaN(value))) {
            return false; // Required field, so empty/null/NaN values should fail with required message
          }
          return Number.isInteger(Number(value));
        })
        .test('is-positive', 'Experience must be a positive number', function (value) {
          // Check if value is empty, null, undefined, or NaN
          if (value === null || value === undefined || value === '' ||
              (typeof value === 'string' && value.trim() === '') ||
              (typeof value === 'number' && isNaN(value))) {
            return false; // Required field, so empty/null/NaN values should fail with required message
          }
          return Number(value) >= 0;
        }),
      language: Yup.string().required('Atleast one language is required'),
      sportsCategories: Yup.array()
        .of(Yup.string().required('Category is required'))
        .min(1, 'At least one sports category must be selected')
        .required('At least one sports category must be selected'),
        accountType: Yup.string().required('Account type is required'),
    
      academyId: Yup.string().when('accountType', {
        is: 'Joining via Academy',
        then: (schema) => schema.required('Academy is required'),
        otherwise: (schema) => schema.notRequired(),
      }),
      academyStartDate: Yup.string().when('accountType', {
        is: 'Joining via Academy',
        then: (schema) => schema.required('Start date is required'),
        otherwise: (schema) => schema.notRequired(),
      }),
      academyEndDate: Yup.string().when('accountType', {
        is: 'Joining via Academy',
        then: (schema) => schema.notRequired(),
        otherwise: (schema) => schema.notRequired(),
      }),
      selectedDays: Yup.array().when('accountType', {
        is: 'Joining via Academy',
        then: (schema) => schema.min(1, 'At least one day is required').required('Days are required'),
        otherwise: (schema) => schema.notRequired(),
      }),
      academyStartTime: Yup.string().when('accountType', {
        is: 'Joining via Academy',
        then: (schema) => schema.required('Start time is required'),
        otherwise: (schema) => schema.notRequired(),
      }),
      academyEndTime: Yup.string().when('accountType', {
        is: 'Joining via Academy',
        then: (schema) => schema.required('End time is required'),
        otherwise: (schema) => schema.notRequired(),
      }),
      coachShare: Yup.number()
        .typeError('Coach share must be a number')
        .min(0, 'Coach share must be at least 0')
        .max(100, 'Coach share must be at most 100')
        .when('accountType', {
          is: 'Joining via Academy',
          then: (schema) => schema.required('Coach share is required'),
          otherwise: (schema) => schema.notRequired(),
        }),
      academyShare: Yup.number()
        .typeError('Academy share must be a number')
        .when('accountType', {
          is: 'Joining via Academy',
          then: (schema) => schema.required('Academy share is required'),
          otherwise: (schema) => schema.notRequired(),
        }),
      // academyAvailability: Yup.object().when('accountType', {
      //   is: 'Joining via Academy',
      //   then: (schema) => schema.required('Academy availability is required'),
      //   otherwise: (schema) => schema.notRequired(),
      // }),
  });

  const formik = useFormik({
    initialValues: {
      firstName: '',
      lastName: '',
      gender: '',
      email: '',
      mobile: '',
      dob: '',
      password: '',
      confirmPassword: '',
      profileImg: '',
      age: '',
      alternateMobile: '',
      linkedFacilities: [
        {
          name: '',
          addressLine1: '',
          addressLine2: '',
          city: '',
          state: '',
          pinCode: '',
          country: '',
          amenities: '',
          location: { coordinates: [] },
        },
      ],
      experience: '',
      language: '',
      sportsCategories: [],
      privacyPolicyAccepted: true,
      coachingQualifications: [{ description: '', image: '' }],
      coachingExperience: [{ description: '', image: '' }],
      playerExperience: [{ description: '', image: '' }],
      award: [{ description: '', image: '' }],
      kycDocuments: {
        // documentName: '',
        documentNumber: '',
        documentImg: []  
      },
      aadhaarNumber: "",
      aadhaarImage: [],
      bankDetails: {
        accountNumber: '',
        accountHolderName: '',
        ifsc: '',
      },
      hasGst: false,
      gstNumber: "",
      gstState: "",
      accountType: accountTypeOptions[0],
      academyId: "",
      academyName: "",
      coachShare: '0',
      academyShare: '0',
    },

    validationSchema: validationSchema,
    onSubmit: async values => {
      // Check if any image is uploading
      if (isAnyImageUploading()) {
        Alert.alert('Upload in Progress', 'Please wait for all images to finish uploading before submitting the form.');
        return;
      }

      if (!privacyAccepted) {
        setShowPrivacyModal(true);
        setSaveData({ handleSubmit: () => { setPrivacyAccepted(true); setShowPrivacyModal(false); formik.handleSubmit(); } });
        return;
      }
      setPrivacyAccepted(false); // Reset for next submit
      const payload = buildPayload({ ...values, privacyPolicyAccepted: true });
      console.log('FINAL PAYLOAD TO BACKEND:', payload); // KEEP: payload to backend
      // return;
      // The rest of your logic, replacing 'filteredData' or 'values' with 'payload' when sending to backend
      try {
        setLoading(true);
        let result;

        // Handle profile image validation
        if (!payload.profileImg) {
          setLoading(false);
          formik.setFieldError('profileImg', 'Select the profile image');
          return;
        }

        // Get coordinates for addresses
        if (payload.linkedFacilities?.length > 0) {
          for (let i = 0; i < payload.linkedFacilities.length; i++) {
            const facility = payload.linkedFacilities[i];
            const addressString = `${facility.name} ${facility.addressLine1} ${facility.city}`;
            const exact_address = await getGeoLocations(addressString);

            if (exact_address?.data) {
              payload.linkedFacilities[i].location = {
                coordinates: [
                  `${exact_address.data.Latitude}`,
                  `${exact_address.data.Longitude}`
                ]
              };
            }
          }
        }

        if (user?.data?.token && user?.data?._id) {
          // Remove email and password for existing users
          delete payload.email;
          delete payload.password;
          result = await axios.patch(
            `${API}/api/coach/${user?.data?._id}`,
            payload,
            {
              headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${user?.data?.token}`,
              },
            }
          );
        } else {
          result = await axios.post(
            `${API}/api/coach`,
            payload,
            {
              headers: {
                'Content-Type': 'application/json',
              },
            }
          );
        }
        console.log("Api response--------------------------------------------->",result) // KEEP: API response
        // Handle response
        if (!result.error) {
          if (user?.data?.token) {
            await updateUserDetails();
            const status = result?.data?.status;
            if (status === 'active') {
              Alert.alert('Details Saved Successfully');
              setTimeout(() => {
                setLoading(false);
                setPageName('Calendar');
                navigation.navigate('Calendar');
              }, 3000);
            } else {
              Alert.alert('Details Saved Successfully. Your application is under process and we will inform you once approved');
              setTimeout(() => {
                setLoading(false);
                setPageName('Professional Details');
                navigation.navigate('ProfessionalDetails', {
                  source: 'registration-patch',
                });
              }, 2000);
            }
          } else {
            // Handle new user registration

            const tokenNew = result?.data?.token;
            const id = result?.data?.data?._id;
            const email = result?.data?.data?.email;
            console.log("Handle New Registration", tokenNew, id); // KEEP: registration debug
            await AsyncStorage.setItem('coachToken', tokenNew);
            await AsyncStorage.setItem('coachId', id);
            await AsyncStorage.setItem('coachEmail', email)
            await AsyncStorage.setItem('coachLoggedIn', 'true');
            await updateUserDetails();

            setTimeout(() => {
              setLoading(false);
              setPageName('Professional Details');
              navigation.navigate('ProfessionalDetails', {
                source: 'registration-post-firstTime',
              });
              setIsHeader(true);
            }, 3000);
          }
        }
      } catch (error) {
        console.error('Config:', error.response.data.error);
        setLoading(false);
        if (error.response?.status === 400) {
          Alert.alert('Error', error.response.data.error);
        } else {
          Alert.alert('Error', 'An error occurred');
        }
      }
    }
  });

  const addAddress = () => {
    formik.setValues({
      ...formik.values,
      linkedFacilities: [
        ...formik.values.linkedFacilities,
        {
          name: '',
          addressLine1: '',
          addressLine2: '',
          city: '',
          state: '',
          pinCode: '',
          country: '',
          // phone: "",
          description: '',
        },
      ],
    });
    // setIsFacilities(true);
  };

  function replaceSlashWithDash(dateString) {
    return dateString.replace(/\//g, '-');
  }

  const showDatePicker = () => {
    setOpen(!open);
  };

  function isOver18YearsOld(selectedDate) {
    // Calculate the minimum date for being 18 years old
    const today = new Date();
    const minAgeDate = new Date(
      today.getFullYear() - 18,
      today.getMonth(),
      today.getDate(),
    );
    const selectedDateObject = new Date(selectedDate);

    return selectedDateObject <= minAgeDate;
  }

  const calculateAge = dob => {
    const currentDate = new Date();
    const dobDate = new Date(dob);

    let age = currentDate.getFullYear() - dobDate.getFullYear();

    // Check if the current date hasn't reached the birth month and day yet
    if (
      currentDate.getMonth() < dobDate.getMonth() ||
      (currentDate.getMonth() === dobDate.getMonth() &&
        currentDate.getDate() < dobDate.getDate())
    ) {
      age--;
    }

    return age;
  };

  // Function to get the first field with validation error
  const getFirstErrorField = (errors) => {
    // Define the order in which fields should be checked for errors (matching UI order)
    const fieldOrder = [
      { path: 'profileImg', ref: 'profileImg' },
      { path: 'firstName', ref: 'firstName' },
      { path: 'lastName', ref: 'lastName' },
      { path: 'mobile', ref: 'mobile' },
      { path: 'alternateMobile', ref: 'alternateMobile' },
      { path: 'email', ref: 'email' },
      { path: 'password', ref: 'password' },
      { path: 'confirmPassword', ref: 'confirmPassword' },
      { path: 'dob', ref: 'dob' },
      { path: 'gender', ref: 'gender' },
      { path: 'linkedFacilities.0.name', ref: 'linkedFacilities' },
      { path: 'linkedFacilities.0.addressLine1', ref: 'linkedFacilities' },
      { path: 'linkedFacilities.0.addressLine2', ref: 'linkedFacilities' },
      { path: 'linkedFacilities.0.city', ref: 'linkedFacilities' },
      { path: 'linkedFacilities.0.state', ref: 'linkedFacilities' },
      { path: 'linkedFacilities.0.pinCode', ref: 'linkedFacilities' },
      { path: 'linkedFacilities.0.country', ref: 'linkedFacilities' },
      { path: 'linkedFacilities', ref: 'linkedFacilities' },
      { path: 'experience', ref: 'experience' },
      { path: 'language', ref: 'language' },
      { path: 'sportsCategories', ref: 'sportsCategories' },
      { path: 'kycDocuments.documentNumber', ref: 'documentNumber' },
      { path: 'kycDocuments.documentImg', ref: 'documentImg' },
      { path: 'aadhaarNumber', ref: 'aadhaarNumber' },
      { path: 'aadhaarImage', ref: 'aadhaarImage' },
      { path: 'bankDetails.accountHolderName', ref: 'accountHolderName' },
      { path: 'bankDetails.accountNumber', ref: 'accountNumber' },
      { path: 'bankDetails.ifsc', ref: 'ifsc' },
      { path: 'gstNumber', ref: 'gstNumber' },
    ];

    // Check each field in order
    for (const field of fieldOrder) {
      const pathParts = field.path.split('.');
      let currentError = errors;

      // Navigate through nested error object
      let hasError = true;
      for (const part of pathParts) {
        if (currentError && typeof currentError === 'object' && currentError[part]) {
          currentError = currentError[part];
        } else {
          hasError = false;
          break;
        }
      }

      if (hasError && typeof currentError === 'string') {
        return field.ref;
      }
    }

    return null;
  };

  // Function to scroll to the first error field
  const scrollToFirstError = () => {
    const firstErrorField = getFirstErrorField(formik.errors);
    console.log("First error field found:", firstErrorField);
    console.log("Current formik errors:", formik.errors);

    if (firstErrorField && fieldRefs.current[firstErrorField] && scrollViewRef.current) {
      console.log("Scrolling to field:", firstErrorField);

      // Measure the position of the error field
      fieldRefs.current[firstErrorField].measureLayout(
        scrollViewRef.current,
        (x, y) => {
          // Scroll to the error field with some offset to show the label and error message
          const scrollOffset = Math.max(0, y - 100); // 100px offset to show label above
          console.log(`Scrolling to position: ${scrollOffset} (field at y: ${y})`);

          scrollViewRef.current.scrollTo({
            y: scrollOffset,
            animated: true, // Smooth scrolling
          });
        },
        (error) => {
          console.warn('Error measuring field position:', error);
        }
      );
    } else {
      console.warn('Cannot scroll to error:', {
        firstErrorField,
        hasFieldRef: !!fieldRefs.current[firstErrorField],
        hasScrollRef: !!scrollViewRef.current
      });
    }
  };

  function handleDateChange(propDate) {
    let date = replaceSlashWithDash(propDate);
    try {
      console.log(replaceSlashWithDash(propDate), 'date'); // KEEP: date change debug
      let isValidAge = isOver18YearsOld(date);
      console.log(isValidAge, '000'); // KEEP: age validation debug
      if (isValidAge) {
        const formattedDate = moment.tz(date, 'Asia/Kolkata').format('YYYY-MM-DD');
        formik.setFieldValue('dob', formattedDate);
        setOpen(false);
      } else {
        formik.setFieldError('dob', 'Must be at least 18 years old');
        Alert.alert('Error', 'Age must be at least 18 years old');
        setOpen(false);
      }
    } catch (error) {
      // Handle validation errors
      formik.setFieldError('dob', error.message);
      setOpen(false);
    }
  }

  const showAlert = () => {
    Alert.alert(
      'Profile Picture change',
      'Do you really want to change,clicking Ok will change and save new profile picture.',
      [
        {
          text: 'Cancel',
          //onPress: handleCancelPress,
          style: 'cancel',
        },
        { text: 'OK', onPress: handleImagePicker },
      ],
      { cancelable: true },
    );
  };

  // Profile image picker - now uses unified function
  async function handleProfileImagePicker() {
    await handleImagePicker('profile');
  }


  const addCoachingExperience = () => {
    formik.setValues({
      ...formik.values,
      coachingExperience: [
        ...formik.values.coachingExperience,
        {
          description: '',
          image: '',
        },
      ],
    });
  };

  const addPlayingExperience = () => {
    formik.setValues({
      ...formik.values,
      playerExperience: [
        ...formik.values.playerExperience,
        {
          playerExperience: '',
          image: '',
        },
      ],
    });
  };

  const addAwards = () => {
    formik.setValues({
      ...formik.values,
      award: [
        ...formik.values.award,
        {
          awardName: '',
          image: '',
        },
      ],
    });
  };

  const addQualification = () => {
    formik.setValues({
      ...formik.values,
      coachingQualifications: [
        ...formik.values.coachingQualifications,
        {
          description: '',
          image: '',
        },
      ],
    });
  };

  // Unified image picker function
  async function handleImagePicker(fieldName, index = null, imageType = null) {
    const options = {
      title: 'Select Image',
      mediaType: 'photo',
      storageOptions: {
        skipBackup: true,
        path: 'images',
      },
    };

    try {
      const result = await launchImageLibrary(options);

      // Check if the user cancelled the picker
      if (result.didCancel) {
        console.log('User cancelled image picker');
        return;
      } else if (result.error) {
        console.error('ImagePicker Error:', result.error);
        return;
      }

      // Handle the selected image
      const imageSizeInMB = result.fileSize / (1024 * 1024);
      if (imageSizeInMB > 10) {
        Alert.alert(
          'Image size is too large',
          'Please select an image smaller than 10MB.',
        );
        return;
      }

      // Set loading state BEFORE API call
      if (fieldName === 'profile') {
        setUploadState('profile', null, true);
      } else if (fieldName === 'kycDocuments' && imageType === 'front') {
        setUploadState('kycFront', null, true);
      } else if (fieldName === 'kycDocuments' && imageType === 'back') {
        setUploadState('kycBack', null, true);
      } else if (fieldName === 'aadhaarImage' && imageType === 'front') {
        setUploadState('aadharFront', null, true);
      } else if (fieldName === 'aadhaarImage' && imageType === 'back') {
        setUploadState('aadharBack', null, true);
      } else {
        setUploadState(fieldName, index, true);
      }

      const imageName = result.assets[0].fileName;
      const formData = new FormData();
      formData.append('image', {
        uri: `${result.assets[0].uri}`,
        type: 'image/jpg',
        name: imageName,
      });

      const response = await axios.post(
        `${API}/api/coach/uploadImage`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        },
      );

      const url = response?.data?.url;

      // Handle different field types
      if (fieldName === 'profile') {
        setProfileImage(url);
        await formik.setFieldValue('profileImg', url);
      } else if (fieldName === 'kycDocuments') {
        if (imageType === 'front') {
          setDocumentImageFront(url);
          const currentImages = formik.values.kycDocuments.documentImg;
          if (currentImages.length === 0) {
            formik.setFieldValue('kycDocuments.documentImg', [{ url }]);
          } else {
            formik.setFieldValue('kycDocuments.documentImg.0.url', url);
          }
        } else if (imageType === 'back') {
          setDocumentImageBack(url);
          const currentImages = formik.values.kycDocuments.documentImg;
          if (currentImages.length === 0) {
            formik.setFieldValue('kycDocuments.documentImg', [{ url: '' }, { url }]);
          } else if (currentImages.length === 1) {
            formik.setFieldValue('kycDocuments.documentImg', [...currentImages, { url }]);
          } else {
            formik.setFieldValue('kycDocuments.documentImg.1.url', url);
          }
        }
      } else if (fieldName === 'aadhaarImage') {
        if (imageType === 'front') {
          setAadharImageFront(url);
          const currentImages = formik.values.aadhaarImage || [];
          // Always place front image at index 0
          if (currentImages.length === 0) {
            // No images yet, create array with front image
            formik.setFieldValue('aadhaarImage', [url]);
          } else if (currentImages.length === 1) {
            // One image exists, place front at index 0 and existing at index 1
            formik.setFieldValue('aadhaarImage', [url, currentImages[0]]);
          } else {
            // Two images exist, just update index 0 (front)
            formik.setFieldValue('aadhaarImage.0', url);
          }
          formik.setFieldTouched('aadhaarImage', true);
        } else if (imageType === 'back') {
          setAadharImageBack(url);
          const currentImages = formik.values.aadhaarImage || [];
          // Always place back image at index 1
          if (currentImages.length === 0) {
            // No images yet, create array with empty front and back image
            formik.setFieldValue('aadhaarImage', ['', url]);
          } else if (currentImages.length === 1) {
            // One image exists (front), add back as second
            formik.setFieldValue('aadhaarImage', [currentImages[0], url]);
          } else {
            // Two images exist, just update index 1 (back)
            formik.setFieldValue('aadhaarImage.1', url);
          }
          formik.setFieldTouched('aadhaarImage', true);
        }
      } else {
        // Handle professional details images
        await formik.setFieldValue(`${fieldName}.${index}.image`, url);

        if (fieldName === 'coachingQualifications') {
          const updatedImages = [...coachingQualificationsImages];
          updatedImages[index] = {
            description: formik.values.coachingQualifications[index].description,
            image: url,
          };
          setCoachingQualificationsImages([...updatedImages]);
        } else if (fieldName === 'award') {
          const updatedImages = [...awardImages];
          updatedImages[index] = {
            description: formik.values.award[index].description,
            image: url,
          };
          setAwardImages([...updatedImages]);
        } else if (fieldName === 'playerExperience') {
          const updatedImages = [...playingExperienceImages];
          updatedImages[index] = {
            description: formik.values.playerExperience[index].description,
            image: url,
          };
          setPlayingExperienceImages([...updatedImages]);
        } else if (fieldName === 'coachingExperience') {
          const updatedImages = [...coachingExperienceImages];
          updatedImages[index] = {
            description: formik.values.coachingExperience[index].description,
            image: url,
          };
          setCoachingExperienceImages([...updatedImages]);
        }
      }
    } catch (error) {
      console.error('Error during image selection:', error);
    } finally {
      // Clear loading state
      if (fieldName === 'profile') {
        setUploadState('profile', null, false);
      } else if (fieldName === 'kycDocuments' && imageType === 'front') {
        setUploadState('kycFront', null, false);
      } else if (fieldName === 'kycDocuments' && imageType === 'back') {
        setUploadState('kycBack', null, false);
      } else if (fieldName === 'aadhaarImage' && imageType === 'front') {
        setUploadState('aadharFront', null, false);
      } else if (fieldName === 'aadhaarImage' && imageType === 'back') {
        setUploadState('aadharBack', null, false);
      } else {
        setUploadState(fieldName, index, false);
      }
    }
  }

  // async function updateDb(obj) {
  //   console.log('inside update db', obj);
  //   try {
  //     const response = await axios.patch(
  //       `${API}/api/coach/${user?.data?.id}`,
  //       obj,
  //       {
  //         headers: {
  //           'Content-Type': 'application/json',
  //           Authorization: `Bearer ${user?.data?.token}`,
  //         },
  //       },
  //     );
  //     console.log('responseeee', response?.data);
  //   } catch (error) {
  //     console.log('error', error);
  //   }
  // }
  // async function updateDb(obj) {
  //   console.log('inside update db', obj);
  //   try {
  //     const response = await axios.patch(
  //       `${API}/api/coach/${user?.data?.id}`,
  //       obj,
  //       {
  //         headers: {
  //           'Content-Type': 'application/json',
  //           Authorization: `Bearer ${user?.data?.token}`,
  //         },
  //       },
  //     );
  //     // console.log('responseeee', response?.data);
  //     updateUserDetails();
  //   } catch (error) {
  //     console.log('error', error);
  //   }
  // }

  const handleReset = () => {
    formik.resetForm(); // Reset the form
    setProfileImage('');
    if (loginToken && loginToken !== '') {
      setPageName('Dashboard');
      navigation.navigate('Dashboard');
    } else {
      setPageName('Login');
      navigation.navigate('Login');
    }
  };

  // Helper to get the first error field key (dot notation for nested fields)
  function getFirstErrorFieldKey(errors) {
    function findKey(obj, prefix = "") {
      for (const key in obj) {
        if (typeof obj[key] === "string" && obj[key]) {
          return prefix ? `${prefix}.${key}` : key;
        } else if (typeof obj[key] === "object" && obj[key] !== null) {
          const nested = findKey(obj[key], prefix ? `${prefix}.${key}` : key);
          if (nested) return nested;
        }
      }
      return null;
    }
    return findKey(errors);
  }

  // Helper to scroll to a field by key
  function scrollToField(fieldKey) {
    if (!fieldKey || !scrollViewRef.current) return;
    // Remove array indices for fieldRefs lookup
    const cleanKey = fieldKey.replace(/\.[0-9]+/g, "");
    const ref = fieldRefs.current[cleanKey];
    if (ref && ref.measure) {
      ref.measure((fx, fy, width, height, px, py) => {
        scrollViewRef.current.scrollTo({ y: py - 100, animated: true });
      });
    }
  }

  // Helper function to check if an object is serializable
  function isSerializable(obj) {
    try {
      JSON.stringify(obj);
      return true;
    } catch (e) {
      return false;
    }
  }

  // Helper to check if a day (e.g. 'Monday') falls between two dates
  function isDayBetweenDates(day, startDate, endDate) {
    // day: string, e.g. 'Monday'
    // startDate, endDate: YYYY-MM-DD
    const daysOfWeek = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    const start = new Date(startDate);
    const end = new Date(endDate);
    for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
      if (daysOfWeek[d.getDay()] === day) return true;
    }
    return false;
  }

  function validateAcademyFields(values) {
    if (values.accountType !== 'Joining via Academy') return { valid: true };
    const todayStr = new Date().toISOString().split('T')[0];
    const selectedDate = values.academyStartDate?.replace(/\//g, '-') || '';
    const now = new Date();
    const startTimeStr = values.academyStartTime;
    const endTimeStr = values.academyEndTime;
    const startDate = selectedDate;
    let endDate = values.academyEndDate?.replace(/\//g, '-') || '';
    if (!endDate && startDate) {
      // Add 10 years
      const [year, month, day] = startDate.split('-');
      endDate = `${parseInt(year, 10) + 10}-${month}-${day}`;
    }
    // Validate start time (if today)
    if (selectedDate === todayStr && startTimeStr) {
      const picked = new Date(`${selectedDate}T${startTimeStr}`);
      const minStart = new Date(now.getTime() + 15 * 60000);
      if (picked < minStart) {
        return { valid: false, message: 'Start time must be at least 15 minutes from now' };
      }
    }
    // Validate end time after start time
    if (startTimeStr && endTimeStr) {
      const start = new Date(`${startDate}T${startTimeStr}`);
      const end = new Date(`${startDate}T${endTimeStr}`);
      if (end <= start) {
        return { valid: false, message: 'End time must be after start time' };
      }
    }
    // Validate every selected day falls between start and end date
    if (values.selectedDays && values.selectedDays.length > 0 && startDate && endDate) {
      // Always work with an array of strings
      const selectedDayValues = values.selectedDays.map(day => typeof day === 'string' ? day : day.value);
      const invalidDays = selectedDayValues.filter(day =>
        !isDayBetweenDates(day, startDate, endDate)
      );
      if (invalidDays.length > 0) {
        console.log("invalid days: ", invalidDays); // KEEP: invalid days validation
        const dayWord = invalidDays.length === 1 ? 'This day does not fall' : 'These days do not fall';
        return { valid: false, message: `${dayWord} between the given dates:\n${invalidDays.join('\n')}` };
      }
    }
    return { valid: true };
  }

  // Update handleFormSubmit to autoscroll to first error and check for serializability
  const handleFormSubmit = async () => {
    console.log("Submit triggered"); // KEEP: form submit trigger

    // Check if formik.values is serializable before validation
    if (!isSerializable(formik.values)) {
      console.log('Non-serializable value in formik.values', formik.values); // KEEP: serialization debug
      // Try to find the problematic field
      for (const key in formik.values) {
        if (!isSerializable(formik.values[key])) {
          console.log('Non-serializable field:', key, formik.values[key]); // KEEP: serialization debug
        }
      }
      Alert.alert('Critical Error', 'Form contains non-serializable data. Please contact support.');
      return;
    }

    // Validate the form first
    const errors = await formik.validateForm();
    console.log("Validation errors:", errors); // KEEP: validation errors

    // If there are validation errors, scroll to the first error
    if (Object.keys(errors).length > 0) {
      // Mark all fields as touched to show error messages
      const touchedFields = {
        firstName: true,
        lastName: true,
        gender: true,
        email: true,
        mobile: true,
        alternateMobile: true,
        dob: true,
        password: true,
        confirmPassword: true,
        experience: true,
        language: true,
        sportsCategories: true,
        linkedFacilities: [{
          name: true,
          addressLine1: true,
          addressLine2: true,
          city: true,
          state: true,
          pinCode: true,
          country: true,
        }],
        kycDocuments: {
          documentNumber: true,
          documentImg: true,
        },
        bankDetails: {
          accountNumber: true,
          accountHolderName: true,
          ifsc: true,
        },
        gstNumber: true,
      };

      await formik.setTouched(touchedFields);

      // Find and scroll to the first error field
      setTimeout(() => {
        scrollToFirstError();
      }, 200);

      return; // Don't proceed with submission
    }

    // If no errors, proceed with normal submission
    if (formik.values.age == '') {
      const age = await calculateAge(new Date(formik.values.dob));
      await formik.setFieldValue('age', age);
    }

    // Validate academy fields before payload
    const academyValidation = validateAcademyFields(formik.values);
    if (!academyValidation.valid) {
      Alert.alert('Validation Error', academyValidation.message);
      return;
    }

    formik.handleSubmit();
  };

  const toggleShowPassword = type => {
    if (type == 'password') {
      setShowPassword(!showPassword);
    } else if (type == 'confirm') {
      setShowConfirmPassword(!showConfirmPassword);
    }
  };
  const handlePolicyModal = (formik) => {
    setSaveData(formik)
    setModalVisible(true)
  }
  // KYC image pickers - now use unified function
  async function handleImagePickerFront() {
    await handleImagePicker('kycDocuments', null, 'front');
  }

  async function handleImagePickerBack() {
    await handleImagePicker('kycDocuments', null, 'back');
  }

  // Aadhar image pickers - use unified function
  async function handleAadharImagePickerFront() {
    await handleImagePicker('aadhaarImage', null, 'front');
    // Trigger validation after image upload
    setTimeout(() => {
      formik.validateField('aadhaarImage');
    }, 100);
  }

  async function handleAadharImagePickerBack() {
    await handleImagePicker('aadhaarImage', null, 'back');
    // Trigger validation after image upload
    setTimeout(() => {
      formik.validateField('aadhaarImage');
    }, 100);
  }

  const toggleStateDropdown = () => {
    setIsStateDropDown((prev)=>{
      return !prev
    }); // Toggle the state dropdown visibility
  };

  const handleSelectState = (stateCode) => {
    setSelectedState(stateCode);
    formik.setFieldValue('gstState', stateCode);
    // console.log("---->>selected", stateCode); // Log the current selected state
    toggleStateDropdown();
  };

  const handleAccountChange = ()=> {
    setAccountType(accountTypeOptions[1])
  }

  // Keep academyShare always in sync with coachShare
  useEffect(() => {
    if (formik.values.accountType === 'Joining via Academy') {
      let num = parseFloat(formik.values.coachShare);
      if (isNaN(num)) {
        formik.setFieldValue('academyShare', '0');
      } else {
        formik.setFieldValue('academyShare', (100 - num).toFixed(2));
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formik.values.coachShare, formik.values.accountType]);

  // Add this after selectedDays state is defined
  useEffect(() => {
    if (Array.isArray(selectedDays)) {
      formik.setFieldValue('selectedDays', selectedDays, false);
    }
    // eslint-disable-next-line
  }, [selectedDays]);

  useEffect(() => {
    if (
      Array.isArray(formik.values.selectedDays) &&
      JSON.stringify(formik.values.selectedDays) !== JSON.stringify(selectedDays)
    ) {
      setSelectedDays(formik.values.selectedDays);
    }
    // eslint-disable-next-line
  }, [formik.values.selectedDays]);

  // Add a useEffect to sync selectedItems with formik sportsCategories
  useEffect(() => {
    if (Array.isArray(formik.values.sportsCategories)) {
      formik.setFieldValue('sportsCategories', formik.values.sportsCategories, false);
    }
    // eslint-disable-next-line
  }, [formik.values.sportsCategories]);

  const [showPrivacyModal, setShowPrivacyModal] = useState(false);
  const [privacyAccepted, setPrivacyAccepted] = useState(false);

  const [submitAttempted, setSubmitAttempted] = useState(0);

  if(loading) {
    return(
      <View style={{ flex: 1 }}>
            <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" />
              </View>
      </View>
    )
  }

  // Add a helper function to format time
  function formatTime(timeString) {
    if (!timeString) return '';
    // Handles both 'HH:mm' and ISO strings
    const m = moment(timeString, [moment.ISO_8601, 'HH:mm']);
    if (!m.isValid()) return timeString;
    return m.format('hh:mm A');
  }

  // Helper function for next day
  function getNextDay(dateString) {
    const date = new Date(dateString);
    date.setDate(date.getDate() + 1);
    return date.toISOString().split('T')[0];
  }

  // Before the return or render, add:
  const formattedStartDate = formik.values.academyStartDate
    ? formik.values.academyStartDate.replace(/\//g, '-')
    : '';
  const validStartDate = !isNaN(new Date(formattedStartDate).getTime());
  const today = new Date().toISOString().split('T')[0];

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <TermsAndConditionsModal
        modalVisible={showPrivacyModal}
        setModalVisible={setShowPrivacyModal}
        saveData={saveData}
      />
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}>
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          {/* <View style={{ flex: 1 }}> */}
          <ScrollView
                  ref={scrollViewRef}
                  showsVerticalScrollIndicator={false}
                  showsHorizontalScrollIndicator={false}
                  style={styles.scrollView}>
              <View style={styles.mainContainer}>

            {/* form */}
            <View style={styles.formContainer}>
              {/* Display selected profile image */}

              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                <View
                  style={{
                    width: 65,
                    height: 65,
                    borderWidth: 0,
                    marginRight: 10,
                    borderRadius: 50,
                    justifyContent: 'center',
                    alignItems: 'center',
                    backgroundColor: 'lightgray',
                  }}>
                  {imageUploadStates.profile ? (
                    <View style={{
                      width: 64,
                      height: 64,
                      borderRadius: 50,
                      justifyContent: 'center',
                      alignItems: 'center',
                      backgroundColor: '#f0f0f0'
                    }}>
                      <ActivityIndicator size="small" color="#0000ff" />
                      
                    </View>
                  ) : (
                    <Image
                      source={{
                        uri: profileImage
                          ? profileImage
                          : 'data:image/png;base64,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',
                      }}
                      style={{
                        width: 64,
                        height: 64,
                        borderRadius: 50,
                        borderWidth: 0,
                      }}
                    />
                  )}
                </View>

                {/* Button to open image picker */}
                <View style={{ marginRight: 10 }}>
                  <TouchableOpacity
                    onPress={handleProfileImagePicker}
                    disabled={imageUploadStates.profile}
                    >
                    <Icon name="pencil" size={24} color={imageUploadStates.profile ? "gray" : "black"} />
                  </TouchableOpacity>
                </View>

                <TouchableOpacity
                  onPress={() => {
                    profileImage ? deleteImage('profile', null, null, profileImage) : '';
                  }}
                  disabled={imageUploadStates.profile}>
                  <Icon name="trash-o" size={24} color={imageUploadStates.profile ? "gray" : "black"} />
                </TouchableOpacity>
              </View>
              {formik.touched.profileImg && formik.errors.profileImg && (
                <Text style={styles.error}>{formik.errors.profileImg}</Text>
              )}
              <View
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  marginVertical: '2%',
                }}>
                <View>
                  <Text
                    style={{
                      paddingVertical: 10,
                      paddingTop: 20,
                      color: 'black',
                      borderTopWidth: 1,
                      borderColor: 'lightblue',
                      fontWeight: '500',
                      fontSize: 16,
                      fontFamily:
                        'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                    }}>
                    Personal Information
                  </Text>
                </View>

                {/* fname */}
                <View style={styles.boxContainer} ref={(ref) => fieldRefs.current.firstName = ref}>
                  <Text style={styles.label}>First Name <Text style={{color: 'red'}}>*</Text></Text>
                  <TextInput placeholderTextColor="#9CA3AF"

                    keyboardType="default"
                    style={styles.input}
                    onBlur={formik.handleBlur('firstName')}
                    value={formik.values.firstName}
                    onChangeText={formik.handleChange('firstName')}
                  />
                  {formik.touched.firstName && formik.errors.firstName && (
                    <Text style={styles.error}>{formik.errors.firstName}</Text>
                  )}
                </View>
                {/* lname */}
                <View style={styles.boxContainer} ref={(ref) => fieldRefs.current.lastName = ref}>
                  <Text style={styles.label}>Last Name <Text style={{color: 'red'}}>*</Text></Text>
                  <TextInput placeholderTextColor="#9CA3AF"

                    keyboardType="default"
                    style={styles.input}
                    onChangeText={formik.handleChange('lastName')}
                    onBlur={formik.handleBlur('lastName')}
                    value={formik.values.lastName}
                  />
                  {formik.touched.lastName && formik.errors.lastName && (
                    <Text style={styles.error}>{formik.errors.lastName}</Text>
                  )}
                </View>
                {/* mobile */}
                <View style={styles.boxContainer} ref={(ref) => fieldRefs.current.mobile = ref}>
                  <Text style={styles.label}>Mobile <Text style={{color: 'red'}}>*</Text></Text>
                  <View style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    borderWidth: 1,
                    borderColor: '#9CA3AF',
                    borderRadius: 5,
                  }}>
                    <View style={{
                      paddingHorizontal: 10,
                      justifyContent: 'center',
                      alignItems: 'center',
                      borderRightWidth: 1,
                      borderRightColor: '#9CA3AF',
                    }}>
                      <Text style={{ fontSize: 16, color:"#000" }}>+91</Text>
                    </View>

                    <TextInput
                      placeholderTextColor="#9CA3AF"
                      keyboardType="phone-pad"
                      maxLength={10}
                      style={{
                        flex: 1,
                        marginTop: 4,
                        paddingHorizontal: 12,
                        paddingVertical: 8,
                        borderRadius: 6,
                        color: '#333',
                        fontSize: 14,
                      }}
                      onChangeText={formik.handleChange('mobile')}
                      onBlur={formik.handleBlur('mobile')}
                      value={formik.values.mobile}
                      editable={true}
                      autoCorrect={false}
                      autoCapitalize="none"
                      // textContentType="telephoneNumber"
                      returnKeyType="next"
                    />
                  </View>

                  {formik.touched.mobile && formik.errors.mobile && (
                    <Text style={styles.error}>{formik.errors.mobile}</Text>
                  )}

                </View>

                {/* alternate mobile */}
                <View style={styles.boxContainer} ref={(ref) => fieldRefs.current.alternateMobile = ref}>
                  <Text style={styles.label}>Alternate Mobile Number</Text>

                  <View style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    borderWidth: 1,
                    borderColor: '#9CA3AF',
                    borderRadius: 5,
                  }}>
                    <View style={{
                      paddingHorizontal: 10,
                      justifyContent: 'center',
                      alignItems: 'center',
                      borderRightWidth: 1,
                      borderRightColor: '#9CA3AF',
                    }}>
                      <Text style={{ fontSize: 16, color:"#000"}}>+91</Text>
                    </View>

                    <TextInput placeholderTextColor="#9CA3AF"
                      maxLength={10}
                      keyboardType="phone-pad"
                      style={{
                        flex: 1,
                        marginTop: 4,
                        paddingHorizontal: 12,
                        paddingVertical: 8,
                        borderRadius: 6,
                        color: '#333',
                        fontSize: 14,
                      }}
                      alternateMobile
                      onChangeText={formik.handleChange('alternateMobile')}
                      onBlur={formik.handleBlur('alternateMobile')}
                      value={formik.values.alternateMobile}
                    />
                  </View>

                  {formik.touched.alternateMobile &&
                    formik.errors.alternateMobile && (
                      <Text style={styles.error}>
                        {formik.errors.alternateMobile}
                      </Text>
                    )}
                </View>

                {/* email */}
                <View style={styles.boxContainer} ref={(ref) => fieldRefs.current.email = ref}>
                  <Text style={styles.label}>Email-Address <Text style={{color: 'red'}}>*</Text></Text>
                  <TextInput placeholderTextColor="#9CA3AF"

                    style={styles.input}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    onChangeText={formik.handleChange('email')}
                    onBlur={formik.handleBlur('email')}
                    value={formik.values.email}
                    editable={true}
                    autoCorrect={false}
                    // textContentType="emailAddress"
                    returnKeyType="next"
                  />
                  {formik.touched.email && formik.errors.email && (
                    <Text style={styles.error}>{formik.errors.email}</Text>
                  )}
                </View>
                {/* password */}

                {loginToken == '' || loginToken == undefined ? (
                  <>
                    <View style={styles.boxContainer} ref={(ref) => fieldRefs.current.password = ref}>
                      <Text style={styles.label}>Password <Text style={{color: 'red'}}>*</Text></Text>

                      <View style={{ position: 'relative' }}>
                        <TextInput placeholderTextColor="#9CA3AF"

                          style={styles.input}
                          // secureTextEntry
                          secureTextEntry={!showPassword}
                          onChangeText={formik.handleChange('password')}
                          onBlur={formik.handleBlur('password')}
                          value={formik.values.password}
                        />
                        <TouchableOpacity
                          onPress={() => toggleShowPassword('password')}
                          style={{ position: 'absolute', top: 13, right: 10 }}>
                          <Icon1
                            name={showPassword ? 'eye' : 'eye-off'}
                            size={22}
                            color="black"
                          />
                        </TouchableOpacity>
                      </View>
                      {formik.touched.password && formik.errors.password && (
                        <Text style={styles.error}>
                          {formik.errors.password}
                        </Text>
                      )}
                    </View>
                  </>
                ) : (
                  <></>
                )}

                {/* confirm password */}

                {loginToken == '' || loginToken == undefined ? (
                  <>
                    <View style={styles.boxContainer} ref={(ref) => fieldRefs.current.confirmPassword = ref}>
                      <Text style={styles.label}>Confirm Password <Text style={{color: 'red'}}>*</Text></Text>

                      <View style={{ position: 'relative' }}>
                        <TextInput placeholderTextColor="#9CA3AF"

                          style={styles.input}
                          // secureTextEntry
                          secureTextEntry={!showConfirmPassword}
                          onChangeText={formik.handleChange('confirmPassword')}
                          onBlur={formik.handleBlur('confirmPassword')}
                          value={formik.values.confirmPassword}
                        />
                        <TouchableOpacity
                          onPress={() => toggleShowPassword('confirm')}
                          style={{ position: 'absolute', top: 13, right: 10 }}>
                          <Icon1
                            name={showConfirmPassword ? 'eye' : 'eye-off'}
                            size={22}
                            color="black"
                          />
                        </TouchableOpacity>
                      </View>
                      {formik.touched.confirmPassword &&
                        formik.errors.confirmPassword && (
                          <Text style={styles.error}>
                            {formik.errors.confirmPassword}
                          </Text>
                        )}
                    </View>
                  </>
                ) : (
                  <></>
                )}

                {/* dob */}

                <View style={styles.boxContainer} ref={(ref) => fieldRefs.current.dob = ref}>
                  <Text style={styles.label}> Date of Birth <Text style={{color: 'red'}}>*</Text></Text>
                  <TouchableOpacity
                    onPress={showDatePicker}
                    style={styles.calendarIcon}>
                    <View style={styles.inputContainer}>
                      {formik.values.dob ? (
                        <TextInput placeholderTextColor="#9CA3AF"

                          style={styles.textInput}
                          value={formik.values?.dob?.split('T')[0]}
                          editable={false}
                        />
                      ) : (
                        <TextInput placeholderTextColor="#9CA3AF"

                          style={styles.textInput}
                          placeholder="Select Date of Birth"
                          editable={false}
                        />
                      )}

                      <Icon name="calendar" size={20} color="black" />
                    </View>
                  </TouchableOpacity>

                  {open && (
                    <DatePicker
                      mode="calendar"
                      name="dob"
                      selected={
                        formik.values.dob
                          ? formik.values.dob.toString()
                          : new Date().toString()
                      }
                      //selected={formik.values.dob || new Date()} // Provide a default date if `dob` is undefined
                      onMonthChange={(month, year) => console.log(month, year)}
                      onDateChange={handleDateChange}
                    />
                  )}

                  {formik.touched.dob && formik.errors.dob && (
                    <Text style={styles.error}>{formik.errors.dob}</Text>
                  )}
                </View>

                {/* gender */}
                <View style={styles.boxContainer} ref={(ref) => fieldRefs.current.gender = ref}>
                  <Text style={styles.label}>Gender <Text style={{color: 'red'}}>*</Text></Text>

                  <View
                    style={{
                      borderColor: '#e5e7eb',
                      borderWidth: 1,
                      borderRadius: 6,
                      marginVertical: '2%',
                    }}>
                    <Picker
                      style={{
                        width: '100%',
                        color: '#333',
                        fontSize: 14,
                       }}
                      selectedValue={formik.values.gender}
                      onValueChange={(itemValue, itemIndex) =>
                        formik.setFieldValue('gender', itemValue)
                      }>
                      <Picker.Item label="Select gender" value="" />
                      <Picker.Item label="Male" value="male" />
                      <Picker.Item label="Female" value="female" />
                      <Picker.Item label="Others" value="others" />
                    </Picker>
                  </View>
                  {formik.touched.gender && formik.errors.gender && (
                    <Text style={styles.error}>{formik.errors.gender}</Text>
                  )}
                </View>

                

                {/* Affiliation Details Section */}
                <View style={{ marginTop: 20 }}>
                  <Text
                    style={{
                      paddingVertical: 10,
                      paddingTop: 20,
                      color: 'black',
                      borderTopWidth: 1,
                      borderColor: 'lightblue',
                      fontWeight: '500',
                      fontSize: 16,
                      fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                    }}
                  >
                    Affiliation Details
                  </Text>
                  {/* Account Type inside Affiliation Details */}
                  <View style={styles.boxContainer} ref={(ref) => fieldRefs.current.accountType = ref}>
                    <Text style={styles.label}>Are you a part of an academy or working individually? <Text style={{color: 'red'}}>*</Text></Text>
                    <View style={{ flexDirection: 'row', gap: 8 }}>
                      {accountTypeOptions.map((option, idx) => (
                        <CustomRadioButton
                          key={option}
                          selected={formik.values.accountType === option}
                          onPress={() => formik.setFieldValue('accountType', option)}
                          label={option}
                          color="#007AFF"
                          disabled={false}
                          style={idx > 0 ? { marginLeft: 8 } : undefined}
                          radioSize={16}
                          labelFontSize={13}
                        />
                      ))}
                    </View>
                    {formik.errors.accountType && (
                      <Text style={styles.error}>{formik.errors.accountType}</Text>
                    )}
                  </View>
                  {/* Show Academy Dropdown only if Joining via Academy is selected */}
                  {formik.values.accountType === 'Joining via Academy' && (
                    <View style={styles.boxContainer} ref={(ref) => fieldRefs.current.academyRef = ref}>
                      <Text style={styles.label}>Select Academy <Text style={{color: 'red'}}>*</Text></Text>
                      <SelectDropdown
                        data={filteredAcademies}
                        onSelect={(selectedItem, index) => {
                          formik.setFieldValue('academyId', selectedItem._id, true); // triggers validation
                          formik.setFieldValue('academyName', selectedItem.name, false); // no need to validate name
                          formik.setFieldTouched('academyId', true, false); // mark as touched, don't revalidate (already done above)
                        }}
                        renderButton={(_, isOpened) => (
                          <View
                            style={{
                              height: 50,
                              borderRadius: 10,
                              paddingHorizontal: 12,
                              backgroundColor: '#f0f0f0',
                              flexDirection: 'row',
                              alignItems: 'center',
                              justifyContent: 'space-between',
                            }}>
                            <Text style={{ fontSize: 16, color: '#000' }}>
                              {formik.values.academyName
                                ? formik.values.academyName
                                : 'type to seach'}
                            </Text>
                            <FontAwesome
                              name={isOpened ? 'chevron-up' : 'chevron-down'}
                              size={18}
                              color="#000"
                            />
                          </View>
                        )}
                        renderItem={(item, index, isSelected) => (
                          <View
                            style={{
                              padding: 12,
                              backgroundColor: isSelected ? '#d0e8ff' : 'transparent',
                            }}
                          >
                            <Text style={{ fontSize: 16, color: '#333' }}>{item.name}</Text>
                          </View>
                        )}
                        search
                        searchPlaceHolder="Search academy..."
                        onChangeSearchInputText={handleSearchInput}
                        dropdownStyle={{ borderRadius: 8, backgroundColor: '#f9f9f9' }}
                        searchInputStyle={{
                          borderBottomWidth: 1,
                          borderColor: '#ccc',
                          paddingHorizontal: 10,
                        }}
                        searchInputTxtStyle={{
                          fontSize: 16,
                          color: '#000',
                        }}
                        searchPlaceHolderColor="#999"
                        showsVerticalScrollIndicator={false}
                        disabled={academySearchLoading}
                      />
                      {academySearchLoading && (
                        <Text style={{ color: '#888', marginTop: 5 }}>Loading...</Text>
                      )}
                      {(formik.touched.academyId || submitAttempted > 0) && formik.errors.academyId && (
                        <Text style={styles.error}>{formik.errors.academyId}</Text>
                      )}
                    </View>
                  )}
                  {/* Only show the following fields if Joining via Academy is selected */}
                  {formik.values.accountType === 'Joining via Academy' && (
                    <>
                      {/* Coach Share */}
                      <View style={styles.boxContainer}>
                        <Text style={styles.label}>Coach Share (%) <Text style={{color: 'red'}}>*</Text></Text>
                        <TextInput
                          placeholderTextColor="#9CA3AF"
                          style={styles.input}
                          maxLength={6}
                          keyboardType="numeric"
                          value={formik.values.coachShare}
                          onChangeText={text => {
                            // Allow only valid numeric input
                            if (!/^\d*\.?\d*$/.test(text)) return;
                            // If empty, allow
                            if (text === '') {
                              formik.setFieldValue('coachShare', '');
                              formik.setFieldValue('academyShare', '');
                              return;
                            }
                            // Split integer and decimal parts
                            const [intPart, decPart] = text.split('.');
                            // Prevent integer part > 100
                            if (intPart.length > 0 && parseInt(intPart, 10) > 100) return;
                            // If integer part is 100, only allow up to 2 decimals
                            if (parseInt(intPart, 10) === 100) {
                              if (decPart && decPart.length > 2) return;
                            } else {
                              // If not 100, allow up to 2 decimals
                              if (decPart && decPart.length > 2) return;
                            }
                            // If integer part is 100 and user tries to add more integer digits, block
                            if (intPart === '100' && text.length > 3 && !text.startsWith('100.')) return;
                            // All checks passed, update values
                            formik.setFieldValue('coachShare', text);
                            let num = parseFloat(text);
                            if (isNaN(num)) {
                              formik.setFieldValue('academyShare', '');
                            } else {
                              formik.setFieldValue('academyShare', (100 - num).toFixed(2));
                            }
                          }}
                          onBlur={e => {
                            let text = formik.values.coachShare;
                            let num = parseFloat(text);
                            if (text === '' || isNaN(num)) {
                              formik.setFieldValue('coachShare', '');
                              formik.setFieldValue('academyShare', '');
                              return;
                            }
                            // Clamp and fix decimals
                            if (num > 100) num = 100;
                            if (num < 0.01) num = 0.01;
                            num = Math.floor(num * 100) / 100;
                            formik.setFieldValue('coachShare', num.toString());
                            formik.setFieldValue('academyShare', (100 - num).toFixed(2));
                            formik.handleBlur('coachShare')(e);
                          }}
                        />
                        {formik.touched.coachShare && formik.errors.coachShare && (
                          <Text style={styles.error}>{formik.errors.coachShare}</Text>
                        )}
                      </View>
                      {/* Academy Share */}
                      <View style={styles.boxContainer}>
                        <Text style={styles.label}>Academy Share (%) <Text style={{color: 'red'}}>*</Text></Text>
                        <TextInput
                          placeholderTextColor="#9CA3AF"
                          style={[styles.input, { backgroundColor: '#f0f0f0' }]}
                          maxLength={6}
                          keyboardType="numeric"
                          value={formik.values.academyShare}
                          editable={false}
                        />
                        {formik.touched.academyShare && formik.errors.academyShare && (
                          <Text style={styles.error}>{formik.errors.academyShare}</Text>
                        )}
                      </View>
                      {/* Start Date */}
                      <TouchableOpacity onPress={() => setOpenAcademyStartDate(true)}>
                      <View style={[
                        styles.boxContainer
                      ]}>
                        <Text style={styles.label}>Start Date <Text style={{color: 'red'}}>*</Text></Text>
                        <View style={[
                          styles.inputContainer,
                          {
                            backgroundColor: '#fff', // Always white, never greyed out
                            borderRadius: 6,
                            paddingHorizontal: 0,
                          }
                        ]}>
                          {formik.values.academyStartDate ? (
                            <TextInput
                              pointerEvents="none"
                              placeholderTextColor="#9CA3AF"
                              style={[
                                styles.textInput,
                                { backgroundColor: 'transparent' }
                              ]}
                              value={formik.values.academyStartDate.split('T')[0]}
                              editable={false}
                            />
                          ) : (
                            <TextInput
                              pointerEvents="none"
                              placeholderTextColor="#9CA3AF"
                              style={[
                                styles.textInput,
                                { backgroundColor: 'transparent' }
                              ]}
                              placeholder="Select Start Date"
                              editable={false}
                            />
                          )}
                          <Icon name="calendar" size={20} color="black" />
                        </View>
                      </View>
                      </TouchableOpacity>
                      <Modal visible={openAcademyStartDate} transparent animationType="fade">
                        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: 'rgba(0,0,0,0.2)' }}>
                          <View style={{ backgroundColor: 'white', borderRadius: 10, padding: 6, width: '90%', maxWidth: 400, alignItems: 'center', justifyContent: 'flex-start' }}>
                            <DatePicker
                              mode="calendar"
                              name="academyStartDate"
                              minimumDate={moment().format('YYYY-MM-DD')}
                              selected={formik.values.academyStartDate ? moment(formik.values.academyStartDate, [moment.ISO_8601, 'YYYY-MM-DD', 'YYYY/MM/DD']).format('YYYY-MM-DD') : moment().format('YYYY-MM-DD')}
                              onDateChange={date => {
                                // Validation: End date must be after start date
                                if (formik.values.academyStartDate) {
                                  const start = new Date(formik.values.academyStartDate.split('T')[0]);
                                  const end = new Date(date.split('T')[0]);
                                  if (end <= start) {
                                    Alert.alert('Invalid End Date', 'End date must be after start date.');
                                    return; // Do not update value
                                  }
                                }
                                // Format date to 'YYYY-MM-DD' using moment
                                const formattedDate = moment(date, [moment.ISO_8601, 'YYYY-MM-DD', 'YYYY/MM/DD']).format('YYYY-MM-DD');
                                formik.setFieldValue('academyStartDate', formattedDate, false);
                                formik.setFieldTouched('academyStartDate', true, false);
                                setTimeout(() => {
                                  formik.validateField('academyStartDate');
                                }, 0);
                                setOpenAcademyStartDate(false);
                                // Clear both start and end time when end date changes
                                formik.setFieldValue('academyStartTime', '');
                                formik.setFieldValue('academyEndDate', '');
                                formik.setFieldValue('academyEndTime', '');
                              }}
                              style={{ marginBottom: 0 }}
                            />
                            <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginTop: 6, width: '100%' }}>
                              <TouchableOpacity
                                onPress={() => {
                                  formik.setFieldValue('academyStartDate', '');
                                  setOpenAcademyStartDate(false);
                                  formik.setFieldValue('academyStartTime', '');
                                  formik.setFieldValue('academyEndDate', '');
                                  formik.setFieldValue('academyEndTime', '');
                                }}
                                style={{ padding: 8 }}
                              >
                                <Text style={{ color: 'red' }}>Clear</Text>
                              </TouchableOpacity>
                              <TouchableOpacity
                                onPress={() => {
                                  const today = moment().format('YYYY-MM-DD');
                                  formik.setFieldValue('academyStartDate', today);
                                  setOpenAcademyStartDate(false);
                                  formik.setFieldValue('academyStartTime', '');
                                  formik.setFieldValue('academyEndDate', '');
                                  formik.setFieldValue('academyEndTime', '');
                                }}
                                style={{ padding: 8 }}
                              >
                                <Text style={{ color: 'blue' }}>Today</Text>
                              </TouchableOpacity>
                            </View>
                          </View>
                        </View>
                      </Modal>
                      {(formik.touched.academyStartDate || submitAttempted > 0) && formik.errors.academyStartDate && (
                        <Text style={styles.error}>{formik.errors.academyStartDate}</Text>
                      )}
                      {/* End Date (optional) */}
                      <View style={styles.boxContainer}>
                      <Text style={styles.label}>End Date (Optional)</Text>
                      <TouchableOpacity
                        onPress={() => {
                          if (formik.values.academyStartDate) {
                            setOpenAcademyEndDate(true);
                          }
                        }}
                        style={{
                          backgroundColor: formik.values.academyStartDate ? '#fff' : '#e0e0e0',
                          borderRadius: 6,
                          marginVertical: '2%',
                        }}
                        disabled={!formik.values.academyStartDate}
                      >
                        
                        <View style={[
                          styles.inputContainer,
                          {
                            backgroundColor: formik.values.academyStartDate ? '#fff' : '#e0e0e0',
                            borderRadius: 6,
                            paddingHorizontal: 0, // Ensures bg color goes edge-to-edge
                          }
                        ]}>
                          {formik.values.academyEndDate ? (
                            <TextInput
                              pointerEvents="none"
                              placeholderTextColor="#9CA3AF"
                              style={[
                                styles.textInput,
                                { backgroundColor: 'transparent' }
                              ]}
                              value={formik.values.academyEndDate.split('T')[0]}
                              placeholder="Select End Date"
                              editable={false}
                            />
                          ) : (
                            <TextInput
                              pointerEvents="none"
                              placeholderTextColor="#9CA3AF"
                              style={[
                                styles.textInput,
                                { backgroundColor: 'transparent' }
                              ]}
                              placeholder="Select End Date"
                              editable={false}
                            />
                          )}
                          <Icon name="calendar" size={20} color="black" />
                        </View>
                      
                      </TouchableOpacity>
                      </View>
                      <Modal visible={openAcademyEndDate} transparent animationType="fade">
                        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: 'rgba(0,0,0,0.2)' }}>
                          <View style={{ backgroundColor: 'white', borderRadius: 10, padding: 6, width: '90%', maxWidth: 400, alignItems: 'center', justifyContent: 'flex-start' }}>
                            <DatePicker
                              mode="calendar"
                              name="academyEndDate"
                              key={formik.values.academyStartDate}
                              minimumDate={validStartDate ? moment(formattedStartDate, [moment.ISO_8601, 'YYYY-MM-DD', 'YYYY/MM/DD']).format('YYYY-MM-DD') : moment().format('YYYY-MM-DD')}
                              selected={
                                formik.values.academyEndDate
                                  ? formik.values.academyEndDate.replace(/\//g, '-')
                                  : undefined
                              }
                              onDateChange={date => {
                                // Validation: End date must be after or same as start date
                                if (formik.values.academyStartDate) {
                                  const start = new Date(formattedStartDate.split('T')[0]);
                                  const end = new Date(date.replace(/\//g, '-').split('T')[0]);
                                  if (end < start) {
                                    Alert.alert('Invalid End Date', 'End date must be the same as or after start date.');
                                    return; // Do not update value
                                  }
                                  console.log('Formik Start date:', formik.values.academyStartDate);
                                  console.log('Converted End date:', end);
                                  console.log('Converted Start date:', start);
                                  console.log('End date validation failed:', { start: formik.values.academyStartDate, end: date });
                                }
                                
                                const formattedEndDate = moment(date, [moment.ISO_8601, 'YYYY-MM-DD', 'YYYY/MM/DD']).format('YYYY-MM-DD');
                                formik.setFieldValue('academyEndDate', formattedEndDate, false);
                                formik.setFieldTouched('academyEndDate', true, false);
                                setTimeout(() => {
                                  formik.validateField('academyEndDate');
                                }, 0);
                                setOpenAcademyEndDate(false);
                                // Clear both start and end time when end date changes
                                formik.setFieldValue('academyStartTime', '');
                                formik.setFieldValue('academyEndTime', '');
                              }}
                              style={{ marginBottom: 0 }}
                            />
                            <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginTop: 6, width: '100%' }}>
                              <TouchableOpacity
                                onPress={() => {
                                  setOpenAcademyEndDate(false);
                                  formik.setFieldValue('academyEndDate', '');
                                  formik.setFieldValue('academyEndTime', '');
                                  formik.setFieldValue('academyStartTime', '');
                                }}
                                style={{ padding: 8 }}
                              >
                                <Text style={{ color: 'red' }}>Clear</Text>
                              </TouchableOpacity>
                              <TouchableOpacity
                                onPress={() => {
                                  if (formik.values.academyStartDate && !isNaN(new Date(formik.values.academyStartDate).getTime())) {
                                    const nextDay = moment(getNextDay(formik.values.academyStartDate), [moment.ISO_8601, 'YYYY-MM-DD', 'YYYY/MM/DD']).format('YYYY-MM-DD');
                                    formik.setFieldValue('academyEndDate', nextDay, false);
                                    formik.setFieldTouched('academyEndDate', true, false);
                                    setTimeout(() => {
                                      formik.validateField('academyEndDate');
                                    }, 0);
                                    setOpenAcademyEndDate(false);
                                    formik.setFieldValue('academyEndTime', '');
                                  }
                                }}
                                style={{ padding: 8 }}
                              >
                                <Text style={{ color: 'blue' }}>Next Day</Text>
                              </TouchableOpacity>
                            </View>
                          </View>
                        </View>
                      </Modal>
                      {(formik.touched.academyEndDate || submitAttempted > 0) && formik.errors.academyEndDate && (
                        <Text style={styles.error}>{formik.errors.academyEndDate}</Text>
                      )}
                      {/* Days of Week */}
                      <View style={styles.boxContainer}>
                        <Text style={styles.label}>Days of Week <Text style={{color: 'red'}}>*</Text></Text>
                        <CustomMultiSelect
                          setSelected={val => {
                            formik.setFieldValue('selectedDays', val);
                            formik.setFieldTouched('selectedDays', true, false);
                          }}
                          data={[
                            { label: 'Mon', value: 'Mon' },
                            { label: 'Tue', value: 'Tue' },
                            { label: 'Wed', value: 'Wed' },
                            { label: 'Thu', value: 'Thu' },
                            { label: 'Fri', value: 'Fri' },
                            { label: 'Sat', value: 'Sat' },
                            { label: 'Sun', value: 'Sun' },
                          ]}
                          label="Select Days"
                          inputStyles={{ color: '#000' }}
                          boxStyles={{ color: '#000' }}
                          dropdownTextStyles={{ color: '#000' }}
                          defaultOption={formik.values.selectedDays}
                        />
                        {(formik.touched.selectedDays || submitAttempted > 0) && formik.errors.selectedDays && (
                          <Text style={styles.error}>{formik.errors.selectedDays}</Text>
                        )}
                      </View>
                      {/* Start Time */}
                      <View style={styles.boxContainer}>
                      <Text style={styles.label}>Start Time <Text style={{color: 'red'}}>*</Text></Text>
                      <TouchableOpacity
                        onPress={() => {
                          if (formik.values.academyStartDate) {
                            setOpenAcademyStartTime(true);
                          }
                        }}
                        style={{
                          backgroundColor: (formik.values.academyStartDate) ? '#fff' : '#e0e0e0',
                          borderRadius: 6,
                          marginVertical: '2%',
                        }}
                        disabled={!formik.values.academyStartDate}
                      >
                        <View style={[styles.inputContainer, { backgroundColor: (formik.values.academyStartDate) ? '#fff' : '#e0e0e0', borderRadius: 6 }]}> 
                          <TextInput
                            pointerEvents="none"
                            placeholderTextColor="#9CA3AF"
                            style={[styles.textInput, { backgroundColor: 'transparent' }]}
                            value={formik.values.academyStartTime ? moment(formik.values.academyStartTime, 'HH:mm').format('hh:mm A') : ''}
                            placeholder="Select Start Time"
                            editable={false}
                          />
                          <Icon name="clock-o" size={20} color="black" />
                        </View>
                      </TouchableOpacity>
                      </View>
                      <DateTimePickerModal
                        isVisible={openAcademyStartTime}
                        mode="time"
                        onConfirm={time => {
                          // Start Time Validation
                          const today = new Date().toISOString().split('T')[0];
                          const selectedDate = formik.values.academyStartDate.replace(/\//g, '-');
                          const now = new Date();
                          const picked = new Date(time);
                          console.log("picked time: ", picked);
                          console.log("today: ", today);
                          console.log("selected date: ", selectedDate);
                          console.log("now: ", now);
                          console.log("time: ", time);
                          if (selectedDate === today) {
                            console.log("selected date is today");
                            const minStart = new Date(now.getTime() + 15 * 60000); // 15 minutes from now
                            if (picked < minStart) {
                              console.log('Start time validation failed:', { selectedDate, picked: picked.toString(), minAllowed: minStart.toString() });
                              Alert.alert('Start time must be at least 15 minutes from now');
                              setOpenAcademyStartTime(false);
                              return;
                            }
                          }
                          formik.setFieldValue('academyStartTime', moment(time, [moment.ISO_8601, 'HH:mm']).format('HH:mm'));
                          setOpenAcademyStartTime(false);
                        }}
                        onCancel={() => setOpenAcademyStartTime(false)}
                        isDisabled={!(formik.values.academyStartDate && formik.values.academyStartTime)}
                        customStyles={{
                          backgroundColor: (formik.values.academyStartDate && formik.values.academyEndDate) ? '#fff' : '#e0e0e0',
                          textColor: (formik.values.academyStartDate && formik.values.academyEndDate) ? '#000' : '#a0a0a0',
                        }}
                      />
                      {(formik.touched.academyStartTime || submitAttempted > 0) && formik.errors.academyStartTime && (
                        <Text style={styles.error}>{formik.errors.academyStartTime}</Text>
                      )}
                      {/* End Time */}
                      <View style={styles.boxContainer}>
                      <Text style={styles.label}>End Time <Text style={{color: 'red'}}>*</Text></Text>
                      <TouchableOpacity
                        onPress={() => {
                          if (formik.values.academyStartDate && formik.values.academyStartTime) {
                            setOpenAcademyEndTime(true);
                          }
                        }}
                        style={{
                          backgroundColor: (formik.values.academyStartDate  && formik.values.academyStartTime) ? '#fff' : '#e0e0e0',
                          borderRadius: 6,
                          marginVertical: '2%',
                        }}
                        disabled={!(formik.values.academyStartDate && formik.values.academyStartTime)}
                      >
                        <View style={[styles.inputContainer, { backgroundColor: (formik.values.academyStartDate &&  formik.values.academyStartTime) ? '#fff' : '#e0e0e0', borderRadius: 6 }]}> 
                          <TextInput
                            pointerEvents="none"
                            placeholderTextColor="#9CA3AF"
                            style={[styles.textInput, { backgroundColor: 'transparent' }]}
                            value={formik.values.academyEndTime ? moment(formik.values.academyEndTime, 'HH:mm').format('hh:mm A') : ''}
                            placeholder="Select End Time"
                            editable={false}
                          />
                          <Icon name="clock-o" size={20} color="black" />
                        </View>
                      </TouchableOpacity>
                      </View>
                      <DateTimePickerModal
                        isVisible={openAcademyEndTime}
                        mode="time"
                        onConfirm={time => {
                          // End time must be after start time (regardless of date)
                          console.log("Minimum date (human):", );
                          if (formik.values.academyStartTime) {
                            const start = moment(formik.values.academyStartTime, [moment.ISO_8601, 'HH:mm']);
                            const end = moment(time, [moment.ISO_8601, 'HH:mm']);
                            console.log("converted start time: ", start);
                            console.log("converted end time: ", end);
                            if (!end.isAfter(start)) {
                              console.log('End time validation failed:', { start: formik.values.academyStartTime, end: time });
                              Alert.alert('End time must be after start time');
                              setOpenAcademyEndTime(false);
                              return;
                            }
                          }
                          formik.setFieldValue('academyEndTime', moment(time, [moment.ISO_8601, 'HH:mm']).format('HH:mm'));
                          setOpenAcademyEndTime(false);
                        }}
                        onCancel={() => setOpenAcademyEndTime(false)}
                        isDisabled={!(formik.values.academyEndDate && formik.values.academyStartTime)}
                        customStyles={{
                          backgroundColor: (formik.values.academyEndDate && formik.values.academyStartTime) ? '#fff' : '#e0e0e0',
                          textColor: (formik.values.academyEndDate && formik.values.academyStartTime) ? '#000' : '#a0a0a0',
                        }}
                      />
                      {(formik.touched.academyEndTime || submitAttempted > 0) && formik.errors.academyEndTime && (
                        <Text style={styles.error}>{formik.errors.academyEndTime}</Text>
                      )}
                    </>
                  )}
                </View>

                {/* address */}
                <Text
                  style={{
                    // paddingVertical: 10,
                    paddingTop: 20,
                    color: 'black',
                    marginTop: 20,
                    borderTopWidth: 1,
                    borderColor: 'lightblue',
                    fontWeight: '500',
                    fontSize: 16,
                  }}>
                  Address
                </Text>
                <View
                  style={{
                    borderWidth: 0,
                    backgroundColor: 'white',
                    elevation: 2,
                    paddingVertical: 10,
                    // paddingHorizontal: 20,
                  }}>
                  {formik.values.linkedFacilities?.map((address, index) => (
                    <View key={index}>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          fontFamily: 'Lato-Bold',
                          marginTop: 30,
                        }}>
                        <Text
                          style={{
                            fontSize: 16,
                            fontWeight: 'bold',
                            color: '#333',
                          }}>
                          Address {`${index + 1}`}
                        </Text>
                        {index + 1 > 1 ? (
                          <TouchableOpacity
                            onPress={() =>
                              formik.setValues(prevState => ({
                                ...prevState,
                                linkedFacilities:
                                  prevState.linkedFacilities.filter(
                                    (_, i) => i !== index,
                                  ),
                              }))
                            }>
                            <Icon name="close" size={18} color="red" />
                          </TouchableOpacity>
                        ) : (
                          <></>
                        )}
                      </View>

                      <View style={styles.boxContainer} ref={index === 0 ? (ref) => fieldRefs.current.linkedFacilities = ref : null}>
                        <Text style={styles.label}>Facility Name <Text style={{color: 'red'}}>*</Text></Text>
                        <TextInput placeholderTextColor="#9CA3AF"

                          style={styles.input}
                          onChangeText={formik.handleChange(
                            `linkedFacilities.${index}.name`,
                          )}
                          onBlur={formik.handleBlur(
                            `linkedFacilities.${index}.name`,
                          )}
                          value={formik.values.linkedFacilities[index].name}
                        />
                        {formik.touched.linkedFacilities?.[index]?.name &&
                          formik.errors.linkedFacilities?.[index]?.name && (
                            <Text style={styles.error}>
                              {formik.errors.linkedFacilities?.[index]?.name}
                            </Text>
                          )}
                      </View>

                      <View style={styles.boxContainer}>
                        <Text style={styles.label}>Pincode <Text style={{color: 'red'}}>*</Text></Text>
                        <TextInput placeholderTextColor="#9CA3AF"
                          maxLength={6}
                          keyboardType="phone-pad"
                          style={styles.input}
                          // onChangeText={formik.handleChange(
                          //   `linkedFacilities.${index}.pinCode`,
                          // )}
                          onChangeText={pin => handlePincodeChange(pin, index)}
                          onBlur={formik.handleBlur(
                            `linkedFacilities.${index}.pinCode`,
                          )}
                          //value={pincode}
                          value={formik.values.linkedFacilities[index].pinCode}
                        />

                        {formik.touched.linkedFacilities?.[index]?.pinCode &&
                          formik.errors.linkedFacilities?.[index]?.pinCode && (
                            <Text style={styles.error}>
                              {formik.errors.linkedFacilities?.[index]?.pinCode}
                            </Text>
                          )}
                      </View>

                      <View style={styles.boxContainer}>
                        <Text style={styles.label}>Address Line 1 <Text style={{color: 'red'}}>*</Text></Text>
                        <TextInput placeholderTextColor="#9CA3AF"

                          style={styles.input}
                          multiline
                          numberOfLines={5}
                          onChangeText={formik.handleChange(
                            `linkedFacilities.${index}.addressLine1`,
                          )}
                          onBlur={formik.handleBlur(
                            `linkedFacilities.${index}.addressLine1`,
                          )}
                          value={
                            formik.values.linkedFacilities[index].addressLine1
                          }
                        />

                        {formik.touched.linkedFacilities?.[index]
                          ?.addressLine1 &&
                          formik.errors.linkedFacilities?.[index]
                            ?.addressLine1 && (
                            <Text style={styles.error}>
                              {
                                formik.errors.linkedFacilities?.[index]
                                  ?.addressLine1
                              }
                            </Text>
                          )}
                      </View>

                      <View style={styles.boxContainer}>
                        <Text style={styles.label}>Address Line 2</Text>
                        <TextInput placeholderTextColor="#9CA3AF"

                          style={styles.input}
                          multiline
                          numberOfLines={5}
                          onChangeText={formik.handleChange(
                            `linkedFacilities.${index}.addressLine2`,
                          )}
                          onBlur={formik.handleBlur(
                            `linkedFacilities.${index}.addressLine2`,
                          )}
                          value={
                            formik.values.linkedFacilities[index].addressLine2
                          }
                        />

                        {formik.touched.linkedFacilities?.[index]
                          ?.addressLine2 &&
                          formik.errors.linkedFacilities?.[index]
                            ?.addressLine2 && (
                            <Text style={styles.error}>
                              {
                                formik.errors.linkedFacilities?.[index]
                                  ?.addressLine2
                              }
                            </Text>
                          )}
                      </View>

                      <View style={styles.boxContainer}>
                        <Text style={styles.label}>City <Text style={{color: 'red'}}>*</Text></Text>
                        <TextInput placeholderTextColor="#9CA3AF"

                          style={styles.input}
                          onChangeText={formik.handleChange(
                            `linkedFacilities.${index}.city`,
                          )}
                          onBlur={formik.handleBlur(
                            `linkedFacilities.${index}.city`,
                          )}
                          value={formik.values.linkedFacilities[index].city}
                        />

                        {formik.touched.linkedFacilities?.[index]?.city &&
                          formik.errors.linkedFacilities?.[index]?.city && (
                            <Text style={styles.error}>
                              {formik.errors.linkedFacilities?.[index]?.city}
                            </Text>
                          )}
                      </View>

                      <View style={styles.boxContainer}>
                        <Text style={styles.label}>State <Text style={{color: 'red'}}>*</Text></Text>
                        <TextInput placeholderTextColor="#9CA3AF"

                          style={styles.input}
                          onChangeText={formik.handleChange(
                            `linkedFacilities.${index}.state`,
                          )}
                          onBlur={formik.handleBlur(
                            `linkedFacilities.${index}.state`,
                          )}
                          value={formik.values.linkedFacilities[index].state}
                        />

                        {formik.touched.linkedFacilities?.[index]?.state &&
                          formik.errors.linkedFacilities?.[index]?.state && (
                            <Text style={styles.error}>
                              {formik.errors.linkedFacilities?.[index]?.state}
                            </Text>
                          )}
                      </View>

                      <View style={styles.boxContainer}>
                        <Text style={styles.label}>Country <Text style={{color: 'red'}}>*</Text></Text>
                        <TextInput placeholderTextColor="#9CA3AF"

                          style={styles.input}
                          onChangeText={formik.handleChange(
                            `linkedFacilities.${index}.country`,
                          )}
                          onBlur={formik.handleBlur(
                            `linkedFacilities.${index}.country`,
                          )}
                          value={formik.values.linkedFacilities[index].country}
                        />
                        {formik.touched.linkedFacilities?.[index]?.country &&
                          formik.errors.linkedFacilities?.[index]?.country && (
                            <Text style={styles.error}>
                              {formik.errors.linkedFacilities?.[index]?.country}
                            </Text>
                          )}
                      </View>

                      <View style={styles.boxContainer}>
                        <Text style={styles.label}>Amenities</Text>

                        <View
                          style={{
                            // borderWidth: 1,
                            borderRadius: 0,
                            borderColor: '#ccc',
                            height: 175,
                          }}>
                          <QuillEditor
                            value={
                              formik?.values?.linkedFacilities?.[index]
                                ?.amenities
                            }
                            autoSize
                            ref={_editor}
                            quill={{ theme: 'snow', placeholder: 'write here ' }}
                            webview={{
                              scrollEnabled: true,
                              style: {
                                borderWidth: 1,
                                borderRadius: 6,
                                borderColor: '#ccc',
                              },
                              nestedScrollEnabled: true,
                              showsHorizontalScrollIndicator: true,
                              showsVerticalScrollIndicator: true,
                            }}
                            onHtmlChange={e => {
                              formik.setFieldValue(
                                `linkedFacilities.${index}.amenities`,
                                e.html,
                              );
                            }}
                            initialHtml={
                              formik?.values?.linkedFacilities?.[index]
                                ?.amenities
                            }
                          />
                        </View>
                      </View>
                    </View>
                  ))}
                </View>

                <View
                  style={{
                    alignSelf: 'flex-start',
                  }}>
                  <TouchableOpacity
                    onPress={addAddress}
                    style={{
                      borderRadius: 10,
                      backgroundColor: 'lightskyblue',
                      borderColor: 'white',
                      borderWidth: 1,
                      paddingVertical: 10,
                      paddingHorizontal: 15,
                      marginRight: 10,
                    }}>
                    <Text
                      style={{
                        fontWeight: '700',
                        color: 'white',
                      }}>
                      Add
                    </Text>
                  </TouchableOpacity>
                </View>
                <View
          style={{  borderWidth: 0 }}>
          <ScrollView
            showsVerticalScrollIndicator={false}
            showsHorizontalScrollIndicator={false}
            nestedScrollEnabled={true}
            >
            <View
              style={{
                backgroundColor: 'white',
                paddingVertical: 10,
                // paddingHorizontal: 20,
                borderWidth: 0,
                elevation: 5,
                borderRadius: 5,
              }}>
              <View
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  marginVertical: '2%',
                }}>
                <Text
                  style={{
                    paddingVertical: 10,
                    color: 'black',
                    fontWeight: '500',
                    fontSize: 16,
                    fontFamily:
                      'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                  }}>
                  Experience
                </Text>

                {/* experience */}
                <View style={styles.boxContainer} ref={(ref) => fieldRefs.current.experience = ref}>
                  <Text style={styles.label}>No. of Experience <Text style={{color: 'red'}}>*</Text></Text>
                  <TextInput
                    placeholderTextColor="#9CA3AF"
                    keyboardType="number-pad"
                    style={styles.input}
                    onBlur={formik.handleBlur('experience')}
                    value={formik?.values?.experience?.toString()}
                    onChangeText={formik.handleChange('experience')}
                    editable={!isUserAuthorized}
                    placeholder="Enter no. of experience"
                  />
                  {formik.touched.experience && formik.errors.experience && (
                    <Text style={styles.error}>{formik.errors.experience}</Text>
                  )}
                </View>
                {/* language */}
                <View style={styles.boxContainer} ref={(ref) => fieldRefs.current.language = ref}>
                  <Text style={styles.label}>Language <Text style={{color: 'red'}}>*</Text></Text>
                  <TextInput
                    placeholderTextColor="#9CA3AF"
                    keyboardType="default"
                    style={styles.input}
                    onBlur={formik.handleBlur('language')}
                    value={formik.values.language}
                    onChangeText={formik.handleChange('language')}
                    editable={!isUserAuthorized}
                    placeholder="Enter languages separated by commma( , )"
                  />
                  {formik.touched.language && formik.errors.language && (
                    <Text style={styles.error}>{formik.errors.language}</Text>
                  )}
                </View>
                {/* sports category */}
                <View style={styles.boxContainer} ref={(ref) => fieldRefs.current.sportsCategories = ref}>
                  <Text style={styles.label}>Sports Category <Text style={{color: 'red'}}>*</Text></Text>

                    <CustomMultiSelect
                    inputStyles={{color:"#000"}}
                    boxStyles={{color:"#000"}}
                    dropdownTextStyles={{color:"#000"}}
                      setSelected={val => {
                        formik.setFieldValue('sportsCategories', val.map(item => item.value));
                        formik.setFieldTouched('sportsCategories', true, false);
                      }}
                      data={categories?.map(item => ({
                        label: item?.name,
                        value: item?.name,
                      }))}
                      label="Newly Selected Sports Categories"
                      defaultOption={formik.values.sportsCategories}
                    />

                  {/* endddddddddddddddddddddddddddddddddddddddddddd */}

                  {(formik.touched.sportsCategories || submitAttempted > 0) && formik.errors.sportsCategories && (
                    <Text style={styles.error}>{formik.errors.sportsCategories}</Text>
                  )}
                </View>
                {/* coaching qualification */}
                <View style={styles.boxContainer}>
                  <Text
                    style={[
                      styles.label,
                      {
                        borderTopWidth: 1,
                        borderColor: 'lightblue',
                        paddingTop: 10,
                      },
                    ]}>
                    Coaching Qualification
                  </Text>

                  {formik?.values?.coachingQualifications?.map(
                    (qualifications, index) => (
                      <View key={index}>
                        <View
                          style={{
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                          }}>
                          <Text
                            style={[
                              styles.label,
                              {
                                alignItems: 'center',
                                width: '80%',
                                fontWeight: '400',
                              },
                            ]}>
                            Qualification {index + 1}
                          </Text>
                          {index + 1 > 1 ? (
                            <TouchableOpacity
                              onPress={() =>
                                formik.setValues(prevState => ({
                                  ...prevState,
                                  coachingQualifications:
                                    prevState.coachingQualifications.filter(
                                      (_, i) => i !== index,
                                    ),
                                }))
                              }
                              style={{ borderWidth: 0, alignSelf: 'center' }}>
                              <Icon name="close" size={23} color="black" />
                            </TouchableOpacity>
                          ) : (
                            <></>
                          )}
                        </View>

                        <TextInput
                          placeholderTextColor="#9CA3AF"
                          style={styles.multiInput}
                          multiline
                          editable={!isUserAuthorized}
                          numberOfLines={5}
                          onChangeText={formik.handleChange(
                            `coachingQualifications.${index}.description`,
                          )}
                          onBlur={formik.handleBlur(
                            `coachingQualifications.${index}.description`,
                          )}
                          value={
                            formik?.values?.coachingQualifications[index]
                              ?.description
                          }
                        />

                        {formik.values.coachingQualifications[index]?.image ===
                          '' ? (
                          <View
                            key={index}
                            style={{
                              width: '99%',
                              height: 120,
                              borderWidth: 1,
                              borderStyle: 'dashed',
                              marginVertical: 10,
                              marginHorizontal: '1%',
                              borderRadius: 6,
                              display: 'flex',
                              justifyContent: 'center',
                              alignItems: 'center',
                            }}>
                            <View
                              style={{
                                flex: 1,
                                justifyContent: 'center',
                                alignItems: 'center',
                              }}>
                              <Svg
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 -5 24 28"
                                stroke-width="1"
                                stroke="grey"
                                width={100}
                                height={100}
                                class="w-8 h-8">
                                <Path
                                  stroke-linecap="round"
                                  stroke-linejoin="round"
                                  d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"></Path>
                              </Svg>
                            </View>
                            <View
                              style={{
                                display: 'flex',
                                justifyContent: 'center',
                                alignItems: 'center',
                              }}>
                              {imageUploadStates.coachingQualifications[index] ? (
                                <View style={{
                                  justifyContent: 'center',
                                  alignItems: 'center',
                                  height: 60
                                }}>
                                  <ActivityIndicator size="small" color="#0000ff" />
                                  <Text style={{ fontSize: 12, marginTop: 5, textAlign: 'center' }}>
                                    Uploading Qualification Image
                                  </Text>
                                </View>
                              ) : (
                                <>
                                  <TouchableOpacity
                                    onPress={() =>
                                      handleImagePicker(
                                        'coachingQualifications',
                                        index,
                                      )
                                    }>
                                    <Text
                                      style={{
                                        color: 'blue',
                                        fontFamily:
                                          'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                                      }}>
                                      {' '}
                                      Upload a file{' '}
                                    </Text>
                                  </TouchableOpacity>
                                  <Text>PNG, JPG, GIF up to 10MB</Text>
                                </>
                              )}
                            </View>
                          </View>
                        ) : (
                          <View
                            key={index}
                            style={{
                              width: '99%',
                              height: 120,
                              borderWidth: 1,
                              borderColor: 'grey',
                              borderStyle: 'dashed',
                              marginVertical: 10,
                              marginHorizontal: '1%',
                              borderRadius: 6,
                            }}>
                            {/* {console.log("qualification images", coachingQualificationsImages)} */}
                            {coachingQualificationsImages[index]?.image && (
                              <Image
                                source={{
                                  uri: coachingQualificationsImages[index]
                                    ?.image,
                                }}
                                style={{
                                  width: '100%',
                                  height: 115,
                                  borderRadius: 6,
                                  resizeMode: 'contain',
                                  //resizeMode:'stretch'
                                }}
                                onError={error =>
                                  console.error('Error loading image:', error)
                                }
                              />
                            )}

                            <TouchableOpacity
                              style={{
                                alignSelf: 'flex-end',
                                position: 'absolute',
                                top: 1,
                                right: 10,
                              }}
                              onPress={() =>
                                deleteImage(
                                  'coachingQualifications',
                                  index,
                                  null,
                                  coachingQualificationsImages[index]?.image,
                                )
                              }>
                              <Icon name="close" size={20} color="red" />
                            </TouchableOpacity>
                          </View>
                        )}
                      </View>
                    ),
                  )}
                  <View
                    style={{
                      alignSelf: 'flex-start',
                      // marginVertical: 10,
                      marginHorizontal: '1%',
                    }}>
                    <TouchableOpacity
                      onPress={addQualification}
                      disabled={isUserAuthorized}
                      style={{
                        borderRadius: 10,
                        backgroundColor: 'lightskyblue',
                        borderColor: 'white',
                        borderWidth: 1,
                        paddingVertical: 10,
                        paddingHorizontal: 15,
                        //marginRight: 10,
                      }}>
                      <Text
                        style={{
                          fontWeight: '700',
                          color: 'white',
                          fontFamily:
                            'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                        }}>
                        Add
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>

                {/* coacing experience */}
                <View style={styles.boxContainer}>
                  <Text
                    style={[
                      styles.label,
                      {
                        borderTopWidth: 1,
                        borderColor: 'lightblue',
                        paddingTop: 10,
                      },
                    ]}>
                    Coaching Experience
                  </Text>

                  {formik?.values?.coachingExperience?.map((_, index) => (
                    <View key={index}>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                        }}>
                        <Text
                          style={[
                            styles.label,
                            {
                              alignItems: 'center',
                              width: '80%',
                              fontWeight: '400',
                            },
                          ]}>
                          Experience {index + 1}
                        </Text>
                        {index + 1 > 1 ? (
                          <TouchableOpacity
                            onPress={() =>
                              formik.setValues(prevState => ({
                                ...prevState,
                                coachingExperience:
                                  prevState.coachingExperience.filter(
                                    (_, i) => i !== index,
                                  ),
                              }))
                            }
                            style={{ borderWidth: 0, alignSelf: 'center' }}>
                            <Icon name="close" size={23} color="black" />
                          </TouchableOpacity>
                        ) : (
                          <></>
                        )}
                      </View>
                      <TextInput
                        placeholderTextColor="#9CA3AF"
                        style={styles.multiInput}
                        multiline
                        editable={!isUserAuthorized}
                        numberOfLines={5}
                        onChangeText={formik.handleChange(
                          `coachingExperience.${index}.description`,
                        )}
                        onBlur={formik.handleBlur(
                          `coachingExperience.${index}.description`,
                        )}
                        value={
                          formik?.values?.coachingExperience[index]?.description
                        }
                      />

                      {formik.values.coachingExperience[index]?.image === '' ? (
                        <View
                          key={index}
                          style={{
                            width: '99%',
                            height: 120,
                            borderWidth: 1,
                            borderStyle: 'dashed',
                            marginVertical: 10,
                            marginHorizontal: '1%',
                            borderRadius: 6,
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                          }}>
                          <View
                            style={{
                              flex: 1,
                              justifyContent: 'center',
                              alignItems: 'center',
                            }}>
                            <Svg
                              xmlns="http://www.w3.org/2000/svg"
                              fill="none"
                              viewBox="0 -5 24 28"
                              stroke-width="1"
                              stroke="grey"
                              width={100}
                              height={100}
                              class="w-8 h-8">
                              <Path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"></Path>
                            </Svg>
                          </View>
                          <View
                            style={{
                              display: 'flex',
                              justifyContent: 'center',
                              alignItems: 'center',
                            }}>
                            {imageUploadStates.coachingExperience[index] ? (
                              <View style={{
                                justifyContent: 'center',
                                alignItems: 'center',
                                height: 60
                              }}>
                                <ActivityIndicator size="small" color="#0000ff" />
                                <Text style={{ fontSize: 12, marginTop: 5, textAlign: 'center' }}>
                                  Uploading Coaching Experience Image
                                </Text>
                              </View>
                            ) : (
                              <>
                                <TouchableOpacity
                                  onPress={() =>
                                    handleImagePicker('coachingExperience', index)
                                  }>
                                  <Text
                                    style={{
                                      color: 'blue',
                                      fontFamily:
                                        'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                                    }}>
                                    {' '}
                                    Upload a file{' '}
                                  </Text>
                                </TouchableOpacity>
                                <Text>PNG, JPG, GIF up to 10MB</Text>
                              </>
                            )}
                          </View>
                        </View>
                      ) : (
                        <View
                          key={index}
                          style={{
                            width: '99%',
                            height: 120,
                            borderWidth: 1,
                            borderColor: 'grey',
                            borderStyle: 'dashed',
                            marginVertical: 10,
                            marginHorizontal: '1%',
                            borderRadius: 6,
                          }}>
                          {/* {console.log("exp images", coachingExperienceImages)} */}
                          {coachingExperienceImages[index]?.image && (
                            <Image
                              source={{
                                uri: coachingExperienceImages[index]?.image,
                              }}
                              style={{
                                width: '100%',
                                height: 115,
                                borderRadius: 6,
                                borderWidth: 1,
                                resizeMode: 'contain',
                              }}
                            />
                          )}

                          <TouchableOpacity
                            style={{
                              alignSelf: 'flex-end',
                              position: 'absolute',
                              top: 1,
                              right: 10,
                            }}
                            onPress={() =>
                              deleteImage(
                                'coachingExperience',
                                index,
                                null,
                                coachingExperienceImages[index]?.image,
                              )
                            }>
                            <Icon name="close" size={20} color="red" />
                          </TouchableOpacity>
                        </View>
                      )}
                    </View>
                  ))}

                  <View
                    style={{
                      alignSelf: 'flex-start',
                      //marginVertical: 10,
                      marginHorizontal: '1%',
                    }}>
                    <TouchableOpacity
                      onPress={addCoachingExperience}
                      disabled={isUserAuthorized}
                      style={{
                        borderRadius: 10,
                        backgroundColor: 'lightskyblue',
                        borderColor: 'white',
                        borderWidth: 1,
                        paddingVertical: 10,
                        paddingHorizontal: 15,
                        //marginRight: 10,
                      }}>
                      <Text
                        style={{
                          fontWeight: '700',
                          color: 'white',
                          fontFamily:
                            'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                        }}>
                        Add
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
                {/* playing experience */}
                <View style={styles.boxContainer}>
                  <Text
                    style={[
                      styles.label,
                      {
                        borderTopWidth: 1,
                        borderColor: 'lightblue',
                        paddingTop: 10,
                      },
                    ]}>
                    Playing Experience
                  </Text>

                  {formik?.values?.playerExperience?.map((_, index) => (
                    <View key={index}>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                        }}>
                        <Text
                          style={[
                            styles.label,
                            {
                              alignItems: 'center',
                              width: '80%',
                              fontWeight: '400',
                            },
                          ]}>
                          Experience {index + 1}
                        </Text>
                        {index + 1 > 1 ? (
                          <TouchableOpacity
                            onPress={() =>
                              formik.setValues(prevState => ({
                                ...prevState,
                                playerExperience:
                                  prevState.playerExperience.filter(
                                    (_, i) => i !== index,
                                  ),
                              }))
                            }
                            style={{ borderWidth: 0, alignSelf: 'center' }}>
                            <Icon name="close" size={23} color="black" />
                          </TouchableOpacity>
                        ) : (
                          <></>
                        )}
                      </View>
                      <TextInput               placeholderTextColor="#9CA3AF"

                        style={styles.multiInput}
                        editable={!isUserAuthorized}
                        multiline
                        numberOfLines={4}
                        onChangeText={formik.handleChange(
                          `playerExperience.${index}.description`,
                        )}
                        onBlur={formik.handleBlur(
                          `playerExperience.${index}.description`,
                        )}
                        value={
                          formik?.values?.playerExperience[index]?.description
                        }
                      />

                      {formik.values.playerExperience[index]?.image === '' ? (
                        <View
                          key={index}
                          style={{
                            width: '99%',
                            height: 120,
                            borderWidth: 1,
                            borderStyle: 'dashed',
                            marginVertical: 10,
                            marginHorizontal: '1%',
                            borderRadius: 6,
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                          }}>
                          <View
                            style={{
                              flex: 1,
                              justifyContent: 'center',
                              alignItems: 'center',
                            }}>
                            <Svg
                              xmlns="http://www.w3.org/2000/svg"
                              fill="none"
                              viewBox="0 -5 24 28"
                              stroke-width="1"
                              stroke="grey"
                              width={100}
                              height={100}
                              class="w-8 h-8">
                              <Path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"></Path>
                            </Svg>
                          </View>
                          <View
                            style={{
                              display: 'flex',
                              justifyContent: 'center',
                              alignItems: 'center',
                            }}>
                            {imageUploadStates.playerExperience[index] ? (
                              <View style={{
                                justifyContent: 'center',
                                alignItems: 'center',
                                height: 60
                              }}>
                                <ActivityIndicator size="small" color="#0000ff" />
                                <Text style={{ fontSize: 12, marginTop: 5, textAlign: 'center' }}>
                                  Uploading Player Experience Image
                                </Text>
                              </View>
                            ) : (
                              <>
                                <TouchableOpacity
                                  onPress={() =>
                                    handleImagePicker('playerExperience', index)
                                  }>
                                  <Text
                                    style={{
                                      color: 'blue',
                                      fontFamily:
                                        'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                                    }}>
                                    {' '}
                                    Upload a file{' '}
                                  </Text>
                                </TouchableOpacity>
                                <Text>PNG, JPG, GIF up to 10MB</Text>
                              </>
                            )}
                          </View>
                        </View>
                      ) : (
                        <View
                          key={index}
                          style={{
                            width: '99%',
                            height: 120,
                            borderWidth: 1,
                            borderColor: 'grey',
                            borderStyle: 'dashed',
                            marginVertical: 10,
                            marginHorizontal: '1%',
                            borderRadius: 6,
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                          }}>
                          {playingExperienceImages[index]?.image && (
                            <Image
                              source={{
                                uri: playingExperienceImages[index]?.image,
                              }}
                              style={{
                                width: '100%',
                                height: 115,
                                borderRadius: 6,
                                resizeMode: 'contain',
                              }}
                            />
                          )}

                          <TouchableOpacity
                            style={{
                              alignSelf: 'flex-end',
                              position: 'absolute',
                              top: 1,
                              right: 10,
                            }}
                            onPress={() =>
                              deleteImage(
                                'playerExperience',
                                index,
                                null,
                                playingExperienceImages[index]?.image,
                              )
                            }>
                            <Icon name="close" size={20} color="red" />
                          </TouchableOpacity>
                        </View>
                      )}
                    </View>
                  ))}
                  <View
                    style={{
                      alignSelf: 'flex-start',
                      //marginVertical: 10,
                      marginHorizontal: '1%',
                    }}>
                    <TouchableOpacity
                      onPress={addPlayingExperience}
                      disabled={isUserAuthorized}
                      style={{
                        borderRadius: 10,
                        backgroundColor: 'lightskyblue',
                        borderColor: 'white',
                        borderWidth: 1,
                        paddingVertical: 10,
                        paddingHorizontal: 15,
                        //marginRight: 10,
                      }}>
                      <Text
                        style={{
                          fontWeight: '700',
                          color: 'white',
                          fontFamily:
                            'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                        }}>
                        Add
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>

                {/* award  */}
                <View style={styles.boxContainer}>
                  <Text
                    style={[
                      styles.label,
                      {
                        borderTopWidth: 1,
                        borderColor: 'lightblue',
                        paddingTop: 10,
                      },
                    ]}>
                    Award
                  </Text>

                  {formik?.values?.award?.map((_, index) => (
                    <View key={index} style={{ paddingHorizontal: 5 }}>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                        }}>
                        <Text
                          style={[
                            styles.label,
                            {
                              alignItems: 'center',
                              width: '80%',
                              fontWeight: '400',
                            },
                          ]}>
                          Award {index + 1}
                        </Text>
                        {index + 1 > 1 ? (
                          <TouchableOpacity
                            onPress={() =>
                              formik.setValues(prevState => ({
                                ...prevState,
                                award: prevState.award.filter(
                                  (_, i) => i !== index,
                                ),
                              }))
                            }
                            style={{ borderWidth: 0, alignSelf: 'center' }}>
                            <Icon name="close" size={23} color="black" />
                          </TouchableOpacity>
                        ) : (
                          <></>
                        )}
                      </View>
                      <TextInput               placeholderTextColor="#9CA3AF"

                        style={styles.multiInput}
                        editable={!isUserAuthorized}
                        multiline
                        numberOfLines={4}
                        onChangeText={formik.handleChange(
                          `award.${index}.description`,
                        )}
                        onBlur={formik.handleBlur(`award.${index}.description`)}
                        value={formik?.values?.award[index]?.description}
                      />

                      {formik.values.award[index]?.image === '' ? (
                        <View
                          style={{
                            width: '99%',
                            height: 120,
                            borderWidth: 1,
                            borderStyle: 'dashed',
                            marginVertical: 10,
                            marginHorizontal: '1%',
                            borderRadius: 6,
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                          }}>
                          <View
                            style={{
                              flex: 1,
                              justifyContent: 'center',
                              alignItems: 'center',
                            }}>
                            <Svg
                              xmlns="http://www.w3.org/2000/svg"
                              fill="none"
                              viewBox="0 -5 24 28"
                              stroke-width="1"
                              stroke="grey"
                              width={100}
                              height={100}
                              class="w-8 h-8">
                              <Path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"></Path>
                            </Svg>
                          </View>
                          <View
                            style={{
                              display: 'flex',
                              justifyContent: 'center',
                              alignItems: 'center',
                            }}>
                            {imageUploadStates.award[index] ? (
                              <View style={{
                                justifyContent: 'center',
                                alignItems: 'center',
                                height: 60
                              }}>
                                <ActivityIndicator size="small" color="#0000ff" />
                                <Text style={{ fontSize: 12, marginTop: 5, textAlign: 'center' }}>
                                  Uploading Award Image
                                </Text>
                              </View>
                            ) : (
                              <>
                                <TouchableOpacity
                                  onPress={() => handleImagePicker('award', index)}>
                                  <Text
                                    style={{
                                      color: 'blue',
                                      fontFamily:
                                        'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                                    }}>
                                    {' '}
                                    Upload a file{' '}
                                  </Text>
                                </TouchableOpacity>
                                <Text>PNG, JPG, GIF up to 10MB</Text>
                              </>
                            )}
                          </View>
                        </View>
                      ) : (
                        <View
                          style={{
                            width: '99%',
                            height: 120,
                            borderWidth: 1,
                            borderColor: 'grey',
                            borderStyle: 'dashed',
                            marginVertical: 10,
                            marginHorizontal: '1%',
                            borderRadius: 6,
                          }}>
                          {awardImages[index]?.image && (
                            <Image
                              source={{ uri: awardImages[index]?.image }}
                              //source={{uri:"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQsJCYxJx8fLT0tMTU3Ojo6Iys/RD84QzQ5OjcBCgoKDQwNGg8PGjclHyU3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3N//AABEIAJQAlAMBIgACEQEDEQH/xAAcAAACAgMBAQAAAAAAAAAAAAAEBQMGAAIHAQj/xAA7EAACAQMCBAQEAwYFBQEAAAABAgMABBEFIQYSMUETIlFhFDJxgSORwQdSobHR4RUWJEJiM1NygvFE/8QAGgEAAwEBAQEAAAAAAAAAAAAAAgMEAQAFBv/EACURAAMAAgICAQQDAQAAAAAAAAABAgMREjEhQRMEMlFhFCPxIv/aAAwDAQACEQMRAD8A51dymQEEDB70tkix8j4NGMc1ERvtVLRHL0CGOX1U1pyP3UH70cEzXhTfpQ8A+YF4JrQwGj+X2rRg46Y+9dwN5gXhMvbNSRJ1yN6lywPmT7g1m46YNYpRrpmhWtPCLbiiEKdxU9lb/G39vaRMqNPIEDN0XPetel2Ym30A+Ca1aMinep3WjwBrfT4C5Bx8RPIct7gDYCgLgNyLII0yxJyNlx7Ur5JfSKKw1L02AEEV5ii4rnK7wQn6rUxuIGUBrOPPqpxQ/JJyw2Bx5yK6FwfDjSi3cmqV4Vu6BkVlP12roPDURTSIwo6ilZsicaQS+nvHW6QY0QB2qN1FF8h71DJD71IHoXvjmrKle3PN3rK47Rz9ZI3JCuCR1Gd635RXRbnR9FnGLy3hmb1PUfcUpuOE9Lc/6Oa8gJOyo3MP416M/Up9omeBroqHfArM7Vab/gma0tfHTUxnHyywfqDSQ6JqQxytbPn1Yr+lNWaAPgsAwTWrCvNS+I0xwt1FHk/uPmvLNLvUVY2tvzgdfMB/Ot+WO9gfFSZG1QsMHKnH60VNZX0bqj2rcznChWDZ/Kn1j+z7iO/jV47e3jDdpZsH+VC8sP2GsVfgqzNke9QeLJFIsiOQ6EMp96usn7MOJ0fBSyx+8Lnb+VQXH7N9chQvM9qMdkct+lLrLP5HRiv0ioXc8c7+IIeVjuQOn2pj8Tf3GlRadEDJZ+JzRooy3Njp6/ag7vTbi1naKVCGU426Gn37OpZbTiZZFh8RYUZ8EZCnpml+GvA3zvdIReG8LtFMjJImzKwwQa9FdR1vhm44j4sj1iLT+bTCE+Kw4BJHXb8qW6xw1p1zxnBaaajwafKo8VkU8sbYO2fyoHL9DZqRFw9HodxZ3EeqTSxTjPhFc4NWTguZm0+SNySqN5SR1FJ7/hhrDiaWyiYy2i4YPnsR3q02dtFaQiG3XC96RfehlZU40FysANjUSc0g8oyKnitWkXPajoIEjHmxXKWydVoDSAcu43rKJe5iRiMivaPijuQut4iwBWPI9RTK1jhiXnmYc4/hShtX8QclunKP+W1F6dbveODKW8NevuaBJJ+DtsJu421CEnHLGu6570iFo5kzy+UHrVgvHZ2+HhJUD5mHYe1DXkiwQiJQC38hR0zk9HLeOsDUFUdhR/BMTCykbcljgD1pPxjL4mrsCc8oqzcEyJbWkUkvy8+9bkf9YK80X3hzh3lxcXCjnJyBjpVujZYFwvatNMuIZbZTGQQR1FaXZ2OKyVpbQ7v/AJMuLlWHXeq7rOoeDCx5ugoi6mMeSW29zVD4n11ZMw2ymV98legoKpsdjlS9lW1+8e4u2fAUdsU6/ZNZjUNc1BXflxa9fqarE8E8jF52xnsO1dR/Y/w/LbWt1q0gKJMvIgI3IHf+NMxfgDNv7ui5cKOUsZ7fOWVmU0Rp9kkemTwtEplLMf417pFoYJ7hxskjZo22/wCpN/5VUl4ImznWoWc9vd4ZfM4zmjLXT35A7041yIfFo2B3oSS7VIivSpnCVPY3fgHMi24IOKXXl9vlDtQ2oStIS6kUrDOfmODSqo7RLNOzSE5NZWqNHjzHesoDdFputF0i8mka0ll0+4RiGSUEx5z2NMDYz2OmgWkRuCB88TB/vtS28u2+HvZGQkqzeb1qvWd/dWsIMc0kch3JViKoqpl9ApDKS/lhypjdX7+IMH8qVT3rEsxbOR1o1+MdRROSRoboDYrPEGzQGpcU6X4BfUOG4HyME20rRml8VT7O2c512Qz6pI3XPpVs0tlh0qNGOCR0rbR7TgjiPUDCo1DTZ28yCScMJDn5QfX60bJFwxFNJavf6nBJbuUZXiUkEU24fFIya0w/h7iSTSplguJMwMfKSdxV8N/HcWwmR1KkZyDXMbiy4XuAA2v3aD3tqZWF1w6lv8BFxPdPzADa23+lL+O9eB83LfkL1W7m1i4a1tGK2y/9SQd/pSLULSO1XkiUAd/U1d7ODRba0WNJrpk/4oATXlnb6Hf3620VjNM5BJeWTYD3FJ4NvW0VTlmVvTKpwbwk3EF2012rLp8Jy7f9w/uiuwxpCEjtLYCOCMAAAY2FCWFm0UIt7GOOG3XoFGwrLy2uEcGF/EOdxjFX44WNEGbK8tb9DNuX5Ix96inkjtYichR1JJ60vXUHtJOWceGQOjVT+INZn1mdrWzbktlOHkH+72FZmzrHO2bg+nrLWvRrea58ZrDrGCbdfLzepqDWbC6eMz2pMkYG69xRNlYwxRqFXp60zhfwTy9VPUV5U56dbZ6eXBHHUlBW5cEg9uoIxitJckE9CO9XjUdBt9TBltfwbjHps31qnalbz2snhXKeG49RsR6iqU00efcOWCI3l3J/OsqEREjPMPzrK0WX3W5PhdHbKbvIVzj3NUa9v5CPIPyqwScW3elSLDEkcqOxZkmGQQQNv51Omr8Paltf6D4Tnq9tJj+lNtJs5bKI7TOOYZO9eyxvNAVYcxNXx7DhWZTFFf3lq37skXMB96HbhvS//wA/EVt7CSEj9azT9BePZyw20tndrKhKSIwZW9DVp4itxrOlwcRWgAnRRFfIvt0f9PypxfcJ2Vxnn4h08H15T/WodE02z4dmlaTiGyu7WZCk1ssZHOD96anvsW1+CiXc8iR8ucEjrUNpN4KFty+evpVy1LhThnTpVOo6/dBJV8SNIrfJ5fTPeo45+BbDDQ6bqOoOOnjPyqftRNLRibTJOHdemn5bTeR+wXLGrxZIvDsHx99lr2VcRW4O4HqapR4+urZfC0DSLLTo/wB5F5nNXDhdH4htBdXhbmU/iO/f6VNUcHuV5K4yc1qy16FxC15ZPI8ZgjT5nbpTOPUbcW5nEi4xktnpVZvofjgtnbjwbKP5sd/rQF/yzw/4dZjltl2LDbmov5Dhef8ATP4yuvH+CLibiaXXr74ezyLSJvNJ3c/0pjo0aiJQBjHatP8AAEth4kWxxuMbVva5V/L26ivMzZXdbZ7GPHMxqB5FGMbVKFA+cVDbyDkBFFDzjP2rV0TvaZJGAoHL19azULC11W38G7QH0YbEGtE8m43A7VOh5txuKdFCbnZQLzgzV4rh1tWS4hz5XbY496yuh4Ne07kTfEjnHG+lTWt1BNNF4Y5vXIOf70qtpfDkyCNqfWmv3UbQjUGW8RBhBLucH1NNLmDha6H40UmnO+/Mq+Rj9RtT6Svyibor9u6lWlnztuKBvuUrzRnJPrVl/wArQTpjTtatJkPRWOD/AArVuENRRCE+Gfbbll/tS/joarTXZUYrRrhDkgMO1Rm1jVGMoGfWrF/lDWlIJa3j33Pjf2rWbhqfpd6nYxoO/iEmt4UdzlIU2fh6tp7aZcALLF57aQ9j6fekc2mzNKIzGUKtyn2ro2kcFwS8sov2IBz4iJyr9jTNYNJs1kkmgF3OreTlHzehNO4PXkXy30V7h3guHCXdwzeABlsjdvpVhuvileIJJDp2kwb8h+aT3x2pfe6pqr5YTRLGfljiGeQegqo8Q6vOxaFjKx6Nz5yK51OtI1TSe2dBOpW97bcthJzx5wT6mp7e38JcgeauX8N6w+m3aOxPhZ8y9hXWbaaG7gSWFgVcAg1Dll1XkvhpStA8qO4GaDntQp8RVI9qdrGB81RTqGycCp7j2Px5Wn4FcMm2c0SlwB5gMgdcUFcpyPhdqIt1BAPyyCky2mPpJrYyXkmTmxge1TBeSl0bkSYGVb0Hej0k8oBIB9KoikyXJLRucA7jesrTmx0NZTRRySGGZ5kdg4CnbBp1Jq0L27RXVuucYxjY/wB6AjtZmGVcxoPXvWpjUEs+4Vcc1PVaI6S0FaXomnakwex1FYGJ+WQ9Kff5M1JVHgalCR2PiMP4VR4bFkmJKF2bcAUzt4L1sj8TkHQKxFG6k6UyzNwjOIx8brEMQ74Yn+Zqax0Ph+yYTrJLqM0R5gOqZ96TaLodxNdJJfRyGM5wrHNPEItPiWRFihjUtISew60S0vRznfsg1nXyLEvdueZjiK3iAH0H96o+p3+pXSiMuwXvFFnb6nvTvRzDqt5LqN98gOI4/btR9wIUy6RqCTnApNZKo9jBix4F9u2UiLT744IVk9y+KvGiWT6zo8llrAVriIgQXBOXAOwBPffA60ku5iCSoxjqcUx4bu51a8ZjlliVlBPcOuOn9RXSvI76iuceUVnXdDnsrH4u188SNyTp3if9QfWm3AHE3w0y2F6/4bnCE9jVkItrnVdY0zmBhmhK8p9QwGR+ffeuSPFJHcKFbzDuKPjyR5Fp4636Z9DqQV+2x9a0KZzVJ4A4nF7GunXsn46DCE/7gKviqGXrv2qep9MYnryCG1VzuOb60HNbyIxIH0pugz2371K0AdfU0DwphrO5YliPOpWTY1svMsmHboNj60VdWxHyjBFLmLElW2YUty5HzSroLExAwUY/SsocOwGMFvfPWsruRnBFKVA7cicxXq+T8p9vahtTQxQMiq8oOSCg60ze3EdnJ4bckpTl5e5NFaMl/PbNp80BhJQfihMnGd8VZK2zyr8AOg6dNNFHNyv4jkKUxn8/SrbBp/w4MT+STORzCi9KsH0q28G1UyE7l3P50Xfi5lhDxLG3KM+9UzjSWwXb6RpAxVRE7Ixxv6iqtxg6w6FqAWReaVMAdyM70e+osqyPgLKVwM7jNVbWbK7u45Zmz4TR7DO5NBdrpDcMPmmxNw/qSRRrC2PMcZJp1JJzhiWyBVDjkMT4zgo38aaJrDlMNU1S9n0kqGtm15qbi5dOUcoOM1aeFSCZZm5sO6KuOrYPiMB2Pygf+wqg3Mxd2YDJY9KtAuRoujOjuqyrGY1DEZaRt5NupCgIp22z7U2ZZL9VklLihrol4JdY1C9L5CIqK2SQct6nfPKucH9KocMoEvjdVLHb1yacpdnTNAlkYt4045lPfmYFQAfZeYkH1FIbJ1DAA+bqMmmcdHmZcia0TPN8Ndi4tHMePMD3BrsPBHEEeuaahLAXUQxMmdz/8vvXFpEZieZlC57mjNF1a50LVY7mEkAY5l7EelBUbW0Ji/R9CBcb1urcvXpS7RtUt9WsY7y3YFH2Iz8rdwaP2YD0oV+g2vyezqCnMOlKr235xzLs47DvTc4AI6g0NLGQQcUGSdhY7ciDxeTyumCPWsplNDGzkla8qbgyv5f0JLPRpGurVJ1MMsZJlPN/tzhf4VPFp1jbaik8Ek5dyxVWY42OAKLeQrHdyQxgco2OMFt/ehJdUgt7UXM4xMHVQxIBy3t6V6a1J5D2N1YCLxJC6qpO37poaXW40jKQfMfLhRuPrVZu9flM7JHLH4pBeKLBA5cjf8AtUMWv+DOXu7YSXAORkhVAPc0LyfgOdMZvaeJGsjSECTJfJ75NQ6lqUFvai1SEEf7t8hT7moo7q6upgUSLwDH0Vuue9LLm0juIitxJKQjEoowM59T3pLryP4+Ci6wyteSTwjljbfl9KB8UjvVng074mJeY9cgZwcmlKy28UoKWMZPVDzYJ+xBGaZLTCn6i4WmFaFZFnS7mABGDCHxy7nHO3sOvuRXt5crrN/HEjE2VsvMwY55yfmP/kT3oW91ae7tlEgFtBIclYwSX+poF7kmEWtunhxndm9TTUtC7y8vJLq+oNqF0qK2YY88u+xPc/TYAd8Cooy0LAhVdceu1ZFEMJ5fm6YolosKfCHkLdSRisbEeaNGKuCVUnB+YntW86M4VyObbzEbD7etTm15Z0V0AY4wqnY7ZzislhmeUAyiPoThc79KHZv6G/BHENxol6FnybSQgOnTb1A9RXabGaOeBZYWDo45gy7givnqUbCJMeKuS7Z2GKvX7O+KzaldPvZPw2PkZjsv/wB/n9aXS15HRXJcTqDsNsdK1JJQZNRwSJcrzoR4Z6NmpCR8pO/Ue4rO/JvQM0ZJrKIyR1ryh4ILmynpezf4YyORI0lqrNI/zEmk9vM3LEdv9Q2SOy4wML6dTXlZTb6JUIZHkWW9uPFdpCXVST8mT2oWOzjS5iiDOVTOMnJ+UH9aysrEdPQ00km3kKxk+eXBJ+ho65UNZQQMAVwxJI3O9ZWUDKp6A71VsEsprRFjZtiB06n+gpf8FBLdozgkqvlwcYyM5H3rKytnsXfYHqUMcVrDIqDndmHMe23ahZI8XEUJd2UoGPMe9ZWUxMSkaSDwLiQITsdiewz0pqzf6N5GVXcBsMw3GD7VlZQ0FPZE0atIWbLOQo5j1G4qaK3Twg++S5XB3GBmsrKx9BT2KLmUm3VlVE85GFGAaBMjQXMbR7NscnfvWVlMnoF/cjtvAlxJPpX4hzh+tWWVA0R6jAJBFeVlTyU39wFFdSyxq5bBI7CsrKys2Fo//9k="}}
                              style={{
                                width: '100%',
                                height: 115,
                                borderRadius: 10,
                                resizeMode: 'contain',
                              }}
                            />
                          )}

                          <TouchableOpacity
                            style={{
                              alignSelf: 'flex-end',
                              position: 'absolute',
                              top: 1,
                              right: 10,
                            }}
                            onPress={() =>
                              deleteImage(
                                'award',
                                index,
                                null,
                                awardImages[index]?.image,
                              )
                            }>
                            <Icon name="close" size={20} color="red" />
                          </TouchableOpacity>
                        </View>
                      )}
                    </View>
                  ))}

                  <View
                    style={{
                      alignSelf: 'flex-start',
                      //marginVertical: 10,
                      marginHorizontal: '1%',
                    }}>
                    <TouchableOpacity
                      onPress={addAwards}
                      disabled={isUserAuthorized}
                      style={{
                        borderRadius: 10,
                        backgroundColor: 'lightskyblue',
                        borderColor: 'white',
                        borderWidth: 1,
                        paddingVertical: 10,
                        paddingHorizontal: 15,
                        //marginRight: 10,
                      }}>
                      <Text
                        style={{
                          fontWeight: '700',
                          color: 'white',
                          fontFamily:
                            'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                        }}>
                        Add
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>

                {/* {isUserAuthorized ? (
                  <></>
                ) : (
                  <>
                    <View style={{ alignSelf: 'flex-end', marginTop: '10%' }}>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-around',
                          marginVertical: 10,
                          marginHorizontal: 20,
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            loginToken && loginToken !== ''
                              ? navigation.navigate('Dashboard')
                              : navigation.navigate('Login');
                          }}>
                          <Text
                            style={{
                              borderWidth: 1,
                              borderColor: 'grey',
                              padding: 10,
                              marginRight: 10,
                              fontWeight: '700',
                              color: 'black',
                              borderRadius: 10,
                              fontFamily:
                                'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                            }}>
                            Cancel
                          </Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                          disabled={isAnyImageUploading()}
                          onPress={() => {
                            if (isAnyImageUploading()) {
                              Alert.alert('Upload in Progress', 'Please wait for all images to finish uploading before submitting the form.');
                              return;
                            }
                            handlePolicyModal(formik)
                          }}>
                          <Text
                            style={{
                              borderWidth: 1,
                              borderColor: 'white',
                              padding: 10,
                              fontWeight: '700',
                              color: 'white',
                              borderRadius: 10,
                              backgroundColor: isAnyImageUploading() ? 'gray' : 'lightskyblue',
                              fontFamily:
                                'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                            }}>
                            {isAnyImageUploading() ? 'Uploading Images...' : 'Save'}
                          </Text>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </>
                )} */}
              </View>
            </View>
          </ScrollView>
        </View>

                <View style={styles.boxContainer}>
                  <Text style={[styles.label]}>KYC Details</Text>
                </View>

                {/* kycDocuments */}
                <View style={styles.boxContainer} ref={(ref) => fieldRefs.current.documentNumber = ref}>
                  <Text style={styles.label}>PAN Number <Text style={{color: 'red'}}>*</Text></Text>
                  <TextInput 
                    placeholderTextColor="#9CA3AF"
                    maxLength={10}
                    keyboardType="default"
                    placeholder="Enter PAN no."
                    style={styles.input}
                    onBlur={formik.handleBlur('kycDocuments.documentNumber')}
                    value={formik?.values?.kycDocuments?.documentNumber}
                    onChangeText={formik.handleChange(
                      'kycDocuments.documentNumber',
                    )}
                    editable={!isEditable}
                  />
                  {formik.touched.kycDocuments?.documentNumber &&
                    formik.errors.kycDocuments?.documentNumber && (
                      <Text style={styles.error}>
                        {formik.errors.kycDocuments?.documentNumber}
                      </Text>
                    )}
                </View>

                {documentImageFront == '' ? (
                  <View style={styles.boxContainer}>
                    <View
                      style={{
                        width: '99%',
                        height: 120,
                        borderWidth: 1,
                        borderStyle: 'dashed',
                        marginVertical: 10,
                        marginHorizontal: '1%',
                        borderRadius: 6,
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                      }}>
                      <View
                        style={{
                          flex: 1,
                          justifyContent: 'center',
                          alignItems: 'center',
                        }}>
                        <Svg
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 -5 24 28"
                          stroke-width="1"
                          stroke="grey"
                          width={100}
                          height={100}
                          class="w-8 h-8">
                          <Path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"></Path>
                          </Svg>
                        </View>
                        <View style={{ borderWidth: 0, paddingVertical: 5, justifyContent: "center", alignItems: "center" }}>
                          {imageUploadStates.kycFront ? (
                            <View style={{
                              justifyContent: 'center',
                              alignItems: 'center',
                              height: 60
                            }}>
                              <ActivityIndicator size="small" color="#0000ff" />
                              <Text style={{ fontSize: 12, marginTop: 5, textAlign: 'center' }}>
                                Uploading PAN Image
                              </Text>
                            </View>
                          ) : (
                            <>
                              <TouchableOpacity onPress={handleImagePickerFront}>
                                <Text style={{ color: 'blue' }}>
                                  {' '}
                                  Upload a file{' '}
                                </Text>
                              </TouchableOpacity>
                              <Text>PNG, JPG, GIF up to 10MB</Text>
                            </>
                          )}
                        </View>
                      </View>
                    </View>
                  
                ) : (
                  <View style={styles.boxContainer}>
                    <View
                      style={{
                        width: '99%',
                        height: 120,
                        borderWidth: 0,
                        marginVertical: 10,
                        marginHorizontal: '1%',
                        borderRadius: 6,
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        //position:'relative'
                      }}>
                      <Image
                        source={{ uri: documentImageFront }}
                        style={{
                          width: '96%',
                          height: 120,
                          borderRadius: 6,
                          resizeMode: 'cover',
                        }}
                      />
                      <TouchableOpacity
                        style={{
                          alignSelf: 'flex-end',
                          position: 'absolute',
                          top: 1,
                          right: 10,
                        }}
                        onPress={() =>
                          deleteImage('kycDocuments', null, 'front', documentImageFront)
                        }>
                        {isEditable ? (
                          <></>
                        ) : (
                          <Icon name="close" size={20} color="red" />
                        )}
                      </TouchableOpacity>
                    </View>
                  </View>
                )}


                {/* Show error message for document images */}
                <View>
                  {formik.touched.kycDocuments?.documentImg &&
                    formik.errors.kycDocuments?.documentImg && (
                      <Text style={styles.error}>
                        {typeof formik.errors.kycDocuments?.documentImg === 'string' 
                          ? formik.errors.kycDocuments?.documentImg 
                          : 'Please upload required documents'}
                      </Text>
                    )}
                </View>

                {/* Aadhar Card Details */}
                <View style={styles.boxContainer}>
                  <Text style={[styles.label]}>Aadhar Details</Text>
                </View>

              
                <View style={styles.boxContainer} ref={(ref) => fieldRefs.current.aadhaarNumber = ref}>
                  <Text style={styles.label}>Aadhar Number <Text style={{color: 'red'}}>*</Text></Text>
                  <TextInput 
                    placeholderTextColor="#9CA3AF"
                    maxLength={12}
                    keyboardType="number-pad"
                    placeholder="Enter Aadhar no."
                    style={styles.input}
                    onBlur={formik.handleBlur('aadhaarNumber')}
                    value={formik?.values?.aadhaarNumber}
                    onChangeText={formik.handleChange(
                      'aadhaarNumber',
                    )}
                    editable={!isEditable}
                  />
                  {(formik.touched.aadhaarNumber || submitAttempted > 0) &&
                    formik.errors.aadhaarNumber && (
                      <Text style={styles.error}>
                        {formik.errors.aadhaarNumber}
                      </Text>
                    )}
                </View>

                {(!formik.values.aadhaarImage || formik.values.aadhaarImage.length === 0 || !formik.values.aadhaarImage[0]) ? (
                  <View style={styles.boxContainer} ref={(ref) => fieldRefs.current.aadhaarImage = ref}>
                    <Text style={styles.label}>Aadhar Front Image <Text style={{color: 'red'}}>*</Text></Text>
                    <View
                      style={{
                        width: '99%',
                        height: 120,
                        borderWidth: 1,
                        borderStyle: 'dashed',
                        marginVertical: 10,
                        marginHorizontal: '1%',
                        borderRadius: 6,
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                      }}>
                      <View
                        style={{
                          flex: 1,
                          justifyContent: 'center',
                          alignItems: 'center',
                        }}>
                        <Svg
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 -5 24 28"
                          stroke-width="1"
                          stroke="grey"
                          width={100}
                          height={100}
                          class="w-8 h-8">
                          <Path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"></Path>
                          </Svg>
                        </View>
                        <View style={{ borderWidth: 0, paddingVertical: 5, justifyContent: "center", alignItems: "center" }}>
                          {imageUploadStates.aadharFront ? (
                            <View style={{
                              justifyContent: 'center',
                              alignItems: 'center',
                              height: 60
                            }}>
                              <ActivityIndicator size="small" color="#0000ff" />
                              <Text style={{ fontSize: 12, marginTop: 5, textAlign: 'center' }}>
                                Uploading Aadhar Front Image
                              </Text>
                            </View>
                          ) : (
                            <>
                              <TouchableOpacity onPress={handleAadharImagePickerFront}>
                                <Text style={{ color: 'blue' }}>
                                  {' '}
                                  Upload front image{' '}
                                </Text>
                              </TouchableOpacity>
                              <Text>PNG, JPG, GIF up to 10MB</Text>
                            </>
                          )}
                        </View>
                      </View>
                    </View>
                  
                ) : (
                  <View style={styles.boxContainer}>
                    <Text style={styles.label}>Aadhar Front Image <Text style={{color: 'red'}}>*</Text></Text>
                    <View
                      style={{
                        width: '99%',
                        height: 120,
                        borderWidth: 0,
                        marginVertical: 10,
                        marginHorizontal: '1%',
                        borderRadius: 6,
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        //position:'relative'
                      }}>
                      <Image
                        source={{ uri: formik.values.aadhaarImage?.[0] || aadharImageFront }}
                        style={{
                          width: '96%',
                          height: 120,
                          borderRadius: 6,
                          resizeMode: 'cover',
                        }}
                      />
                      <TouchableOpacity
                        style={{
                          alignSelf: 'flex-end',
                          position: 'absolute',
                          top: 1,
                          right: 10,
                        }}
                        onPress={() =>
                          deleteImage('aadhaarImage', null, 'front', formik.values.aadhaarImage?.[0] || aadharImageFront)
                        }>
                        {isEditable ? (
                          <></>
                        ) : (
                          <Icon name="close" size={20} color="red" />
                        )}
                      </TouchableOpacity>
                    </View>
                  </View>
                )}

                {(!formik.values.aadhaarImage || formik.values.aadhaarImage.length < 2 || !formik.values.aadhaarImage[1]) ? (
                  <View style={styles.boxContainer}>
                    <Text style={styles.label}>Aadhar Back Image <Text style={{color: 'red'}}>*</Text></Text>
                    <View
                      style={{
                        width: '99%',
                        height: 120,
                        borderWidth: 1,
                        borderStyle: 'dashed',
                        marginVertical: 10,
                        marginHorizontal: '1%',
                        borderRadius: 6,
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                      }}>
                      <View
                        style={{
                          flex: 1,
                          justifyContent: 'center',
                          alignItems: 'center',
                        }}>
                        <Svg
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 -5 24 28"
                          stroke-width="1"
                          stroke="grey"
                          width={100}
                          height={100}
                          class="w-8 h-8">
                          <Path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"></Path>
                          </Svg>
                        </View>
                        <View style={{ borderWidth: 0, paddingVertical: 5, justifyContent: "center", alignItems: "center" }}>
                          {imageUploadStates.aadharBack ? (
                            <View style={{
                              justifyContent: 'center',
                              alignItems: 'center',
                              height: 60
                            }}>
                              <ActivityIndicator size="small" color="#0000ff" />
                              <Text style={{ fontSize: 12, marginTop: 5, textAlign: 'center' }}>
                                Uploading Aadhar Back Image
                              </Text>
                            </View>
                          ) : (
                            <>
                              <TouchableOpacity onPress={handleAadharImagePickerBack}>
                                <Text style={{ color: 'blue' }}>
                                  {' '}
                                  Upload back image{' '}
                                </Text>
                              </TouchableOpacity>
                              <Text>PNG, JPG, GIF up to 10MB</Text>
                            </>
                          )}
                        </View>
                      </View>
                    </View>
                  
                ) : (
                  <View style={styles.boxContainer}>
                    <Text style={styles.label}>Aadhar Back Image <Text style={{color: 'red'}}>*</Text></Text>
                    <View
                      style={{
                        width: '99%',
                        height: 120,
                        borderWidth: 0,
                        marginVertical: 10,
                        marginHorizontal: '1%',
                        borderRadius: 6,
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        //position:'relative'
                      }}>
                      <Image
                        source={{ uri: aadharImageBack }}
                        style={{
                          width: '96%',
                          height: 120,
                          borderRadius: 6,
                          resizeMode: 'cover',
                        }}
                      />
                      <TouchableOpacity
                        style={{
                          alignSelf: 'flex-end',
                          position: 'absolute',
                          top: 1,
                          right: 10,
                        }}
                        onPress={() =>
                          deleteImage('aadhaarImage', null, 'back', aadharImageBack)
                        }>
                        {isEditable ? (
                          <></>
                        ) : (
                          <Icon name="close" size={20} color="red" />
                        )}
                      </TouchableOpacity>
                    </View>
                  </View>
                )}


                {/* Show error message for Aadhar images */}
                <View>
                  {(formik.touched.aadhaarImage || submitAttempted > 0) &&
                    formik.errors.aadhaarImage && (
                      <Text style={styles.error}>
                        {typeof formik.errors.aadhaarImage === 'string'
                          ? formik.errors.aadhaarImage
                          : 'Please upload both front and back images of Aadhar'}
                      </Text>
                    )}
                </View>

                {/* Account Details */}

                <View style={styles.boxContainer}>
                  <Text
                    style={[
                      styles.label,
                      {
                        borderTopWidth: 1,
                        borderColor: 'lightblue',
                        paddingTop: 10,
                      },
                    ]}>
                    Account Details
                  </Text>
                </View>

                {/* account holder name */}
                <View style={styles.boxContainer} ref={(ref) => fieldRefs.current.accountHolderName = ref}>
                  <Text style={styles.label}>Account Holder Name <Text style={{color: 'red'}}>*</Text></Text>
                  <TextInput               placeholderTextColor="#9CA3AF"

                    keyboardType="default"
                    placeholder="Enter name"
                    style={styles.input}
                    onBlur={formik.handleBlur('bankDetails.accountHolderName')}
                    value={formik?.values?.bankDetails?.accountHolderName}
                    onChangeText={formik.handleChange(
                      'bankDetails.accountHolderName',
                    )}
                    editable={!isEditable}
                  />
                  {formik.touched.bankDetails?.accountHolderName &&
                    formik.errors.bankDetails?.accountHolderName && (
                      <Text style={styles.error}>
                        {formik.errors.bankDetails?.accountHolderName}
                      </Text>
                    )}
                </View>
                {/* account number  */}
                <View style={styles.boxContainer} ref={(ref) => fieldRefs.current.accountNumber = ref}>
                  <Text style={styles.label}>Account No. <Text style={{color: 'red'}}>*</Text></Text>
                  <TextInput               placeholderTextColor="#9CA3AF"

                    //keyboardType="default"
                    keyboardType="number-pad"
                    placeholder="Enter account no."
                    style={styles.input}
                    onBlur={formik.handleBlur('bankDetails.accountNumber')}
                    value={formik?.values?.bankDetails?.accountNumber}
                    onChangeText={formik.handleChange(
                      'bankDetails.accountNumber',
                    )}
                    editable={!isEditable}
                  />
                  {formik.touched.bankDetails?.accountNumber &&
                    formik.errors.bankDetails?.accountNumber && (
                      <Text style={styles.error}>
                        {formik.errors.bankDetails?.accountNumber}
                      </Text>
                    )}
                </View>

                {/* Ifsc code */}
                <View style={styles.boxContainer} ref={(ref) => fieldRefs.current.ifsc = ref}>
                  <Text style={styles.label}>IFSC Code <Text style={{color: 'red'}}>*</Text></Text>
                  <TextInput               placeholderTextColor="#9CA3AF"

                    keyboardType="default"
                    placeholder="Enter IFSC code"
                    style={styles.input}
                    onBlur={formik.handleBlur('bankDetails.ifsc')}
                    value={formik?.values?.bankDetails?.ifsc}
                    onChangeText={formik.handleChange('bankDetails.ifsc')}
                    // editable={!formik?.values?.bankDetails?.ifsc}
                    //editable={user?.data?.bankDetails?.ifsc === '' ? null : !formik?.values?.bankDetails?.ifsc}
                    editable={!isEditable}
                  />
                  {formik.touched.bankDetails?.ifsc &&
                    formik.errors.bankDetails?.ifsc && (
                      <Text style={styles.error}>
                        {formik.errors.bankDetails?.ifsc}
                      </Text>
                    )}
                </View>
                
                <View style={{ marginTop: "5%", }}>
                  <Text style={styles.label}>GST Details</Text>
                  <View style={[styles.bookingAlign, {flexDirection: "row", alignItems: "center", gap: 10, marginTop: 5}]}>
                    <Text style={styles.label}>Has GST?</Text>
                    <View style={[styles.radioGroup, {gap: 10}]}>
                        <CustomRadioButton label={"Yes"} onPress={() => formik.setFieldValue("hasGst", true)} disabled={isEditable} selected={formik.values.hasGst}/>
                        <CustomRadioButton label={"No"} onPress={() => formik.setFieldValue("hasGst", false)} disabled={isEditable} selected={!formik.values.hasGst}/>
                    </View>
                  </View>
                  {formik.values.hasGst && (
                    <View style={{gap: 10, marginTop: 10}}>
                      <View ref={(ref) => fieldRefs.current.gstNumber = ref}>
                        <Text style={styles.label}>GST Account No. <Text style={{color: 'red'}}>*</Text></Text>
                        <TextInput
                          placeholderTextColor="#9CA3AF"
                          placeholder="Enter GST number."
                          value={formik.values.gstNumber}
                          onChangeText={(text) => formik.setFieldValue("gstNumber", text)}
                          style={styles.input}
                          editable={!isEditable}
                        />
                        {formik.touched.gstNumber && formik.errors.gstNumber && (
                          <Text style={styles.error}>{formik.errors.gstNumber}</Text>
                        )}
                      </View>

                      <View>
                        <Text style={styles.label}>GST State <Text style={{color: 'red'}}>*</Text></Text>
                        <TouchableOpacity onPress={toggleStateDropdown}>
                          <TextInput
                            style={styles.input}
                            editable={false}
                            pointerEvents="none"
                            placeholder="Select State"
                            placeholderTextColor="#9CA3AF"
                            value={
                              selectedState
                                ? states.find((state) => state.isoCode === selectedState)?.name
                                : states.find((state) => state.isoCode === formik.values.gstState)?.name || ""
                            }
                          />
                        </TouchableOpacity>

                        <Modal visible={isStateDropDown} animationType="slide" transparent={true}>
                          <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: 'rgba(0,0,0,0.2)' }}>
                            <View style={styles.modalContainer}>
                              <ScrollView contentContainerStyle={styles.scrollView}>
                                {states.map((state) => (
                                  <TouchableOpacity
                                    key={state.isoCode}
                                    style={styles.option}
                                    onPress={() => handleSelectState(state.isoCode)}
                                  >
                                    <Text style={styles.optionText}>
                                      {state.name} {selectedState === state.isoCode ? "✓" : ""}
                                    </Text>
                                  </TouchableOpacity>
                                ))}
                              </ScrollView>
                              <TouchableOpacity onPress={toggleStateDropdown} style={styles.closeButton}>
                                <Text style={{color:"#FFF"}}>Close</Text>
                              </TouchableOpacity>
                            </View>
                          </View>
                        </Modal>

                        {formik.touched.gstState && formik.errors.gstState && (
                          <Text style={styles.error}>{formik.errors.gstState}</Text>
                        )}
                      </View>
                      </View>
                    )}

                    
                </View>

                {isEditable ? (
                  <></>
                ) : (
                  <View style={{ alignSelf: 'flex-end', marginTop: '10%' }}>
                    <View
                      style={{
                        flexDirection: 'row',
                        justifyContent: 'space-around',
                        marginVertical: 20,
                        marginHorizontal: 20,
                      }}>
                      <TouchableOpacity onPress={handleReset}>
                        <Text
                          style={{
                            borderWidth: 1,
                            borderColor: 'grey',
                            padding: 10,
                            marginRight: 10,
                            fontWeight: '700',
                            color: 'black',
                            borderRadius: 10,
                            fontFamily:
                              'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                          }}>
                          Cancel
                        </Text>
                      </TouchableOpacity>

                      <TouchableOpacity
                        onPress={async () => {
                          if (!formik.values.profileImg) {
                            Alert.alert(
                              'Profile image required',
                              'Please select a profile image.',
                              [
                                {
                                  text: 'OK',
                                  onPress: () => {
                                    if (scrollViewRef.current) {
                                      scrollViewRef.current.scrollTo({ y: 0, animated: true });
                                    }
                                  }
                                }
                              ]
                            );
                            return;
                          }
                          setSubmitAttempted(prev => prev + 1);
                          await formik.setTouched({
                            academyStartDate: true,
                            academyEndDate: true,
                            academyStartTime: true,
                            academyEndTime: true,
                            selectedDays: true,
                            sportsCategories: true,
                            aadhaarNumber: true,
                            aadhaarImage: true,
                            // Add other fields as needed
                          }, true); // true triggers validation
                          
                          handleFormSubmit();
                        }}
                        style={{
                          borderRadius: 10,
                          backgroundColor: 'lightskyblue',
                          borderColor: 'white',
                          borderWidth: 1,
                          padding: 10,
                        }}>
                        <Text
                          style={{
                            fontWeight: '700',
                            color: 'white',
                            fontFamily:
                              'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                          }}>
                          Save
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                )}
              </View>
            </View>
          
        {/* </View> */}
          </View>
          </ScrollView>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};
export default OneStepForm;

// Consistent spacing constants
const SPACING = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 20,
  xxl: 24,
  xxxl: 32,
};

const styles = StyleSheet.create({
  // Container styles
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  mainContainer: {
    flex: 1,
    backgroundColor: 'lightblue',
    padding: SPACING.md,
  },
  formContainer: {
    backgroundColor: 'white',
    padding: SPACING.xl,
    borderRadius: SPACING.sm,
    elevation: 5,
  },
  sectionContainer: {
    marginVertical: SPACING.md,
  },

  // Field container styles
  fieldContainer: {
    marginBottom: SPACING.lg,
  },

  // Text styles
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: SPACING.xs,
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: 'black',
    paddingVertical: SPACING.md,
    paddingTop: SPACING.xl,
    borderTopWidth: 1,
    borderColor: 'lightblue',
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
  },
  error: {
    fontSize: 12,
    color: 'red',
    marginTop: SPACING.xs,
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
  },

  // Input styles
  input: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#ccc',
    color: '#333',
    fontSize: 14,
    marginTop: SPACING.xs,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 6,
    paddingHorizontal: SPACING.sm,
    marginTop: SPACING.xs,
  },
  textInput: {
    flex: 1,
    height: 50,
    color: '#000',
    paddingHorizontal: SPACING.sm,
  },
  multiInput: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#ccc',
    color: '#333',
    fontSize: 14,
    marginTop: SPACING.xs,
  },

  // Phone input styles
  phoneContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#9CA3AF',
    borderRadius: 5,
    marginTop: SPACING.xs,
  },
  phonePrefix: {
    paddingHorizontal: SPACING.md,
    justifyContent: 'center',
    alignItems: 'center',
    borderRightWidth: 1,
    borderRightColor: '#9CA3AF',
  },
  phoneInput: {
    flex: 1,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    color: '#333',
    fontSize: 14,
  },

  // Profile image styles
  profileImageContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  profileImageWrapper: {
    width: 65,
    height: 65,
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'lightgray',
    marginRight: SPACING.md,
  },
  profileImage: {
    width: 64,
    height: 64,
    borderRadius: 50,
  },
  profileImageButton: {
    marginRight: SPACING.md,
  },

  // Radio button styles
  radioContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: SPACING.lg,
    width: "80%",
  },
  radioGroup: {
    flexDirection: "row",
    alignItems: "center",
  },
  radioOption: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: SPACING.xl,
  },
  radioLabel: {
    fontSize: 16,
    color: '#333',
    marginLeft: SPACING.xs,
  },

  // Editor styles
  editor: {
    padding: 0,
    borderWidth: 0,
    marginVertical: SPACING.xs,
    backgroundColor: 'white',
    minHeight: 150,
    maxHeight: 500,
    borderRadius: 6,
    borderColor: '#ccc',
  },

  // Modal styles
  modalContainer: {
    backgroundColor: 'white',
    borderRadius: SPACING.xl,
    padding: SPACING.xxxl,
    width: '92%',
    maxHeight: '70%',
  },
  option: {
    padding: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
  },
  optionText: {
    fontSize: 16,
    color: '#000',
  },
  closeButton: {
    padding: SPACING.md,
    backgroundColor: '#000',
    alignItems: 'center',
    borderRadius: SPACING.md,
    marginTop: SPACING.lg,
  },

  // Button styles
  buttonContainer: {
    alignSelf: 'flex-end',
    marginTop: SPACING.xxxl,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginVertical: SPACING.xl,
    marginHorizontal: SPACING.xl,
  },
  cancelButton: {
    borderWidth: 1,
    borderColor: 'grey',
    padding: SPACING.md,
    marginRight: SPACING.md,
    fontWeight: '700',
    color: 'black',
    borderRadius: SPACING.md,
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
  },
  submitButton: {
    backgroundColor: 'lightblue',
    padding: SPACING.md,
    borderRadius: SPACING.md,
    fontWeight: '700',
    color: 'black',
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
  },

  // Utility styles
  scrollView: {
    flexDirection: 'column',
  },

  // Legacy styles (to be gradually replaced)
  boxContainer: {
    marginBottom: SPACING.lg, // Updated to use consistent spacing
  },
  calendarIcon: {
    // Removed padding as it's now handled by parent container
  },
});